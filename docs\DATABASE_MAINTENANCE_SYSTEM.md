# Database Maintenance System

## Overview

The Database Maintenance System provides comprehensive tools for preventing and resolving data persistence issues in the ERDB Document Management System. This system was implemented in response to the data persistence issue resolved on July 30, 2025, where orphaned database records remained after incomplete batch delete operations.

## Key Features

### 1. Atomic Delete Operations
- **Transactional Safety**: All delete operations use database transactions to ensure atomicity
- **Comprehensive Cleanup**: Deletes related records across all tables (pdf_documents, form_submissions, cover_images, location_sources)
- **Rollback Mechanism**: Automatic rollback on failure to maintain data consistency
- **Vector Database Integration**: Properly removes embeddings from ChromaDB
- **Verification Steps**: Confirms successful deletion and provides detailed feedback

### 2. Orphaned Record Detection
- **Real-time Detection**: Identifies database records for files that no longer exist
- **Comprehensive Analysis**: Checks all related tables for orphaned data
- **Detailed Reporting**: Provides information about orphaned records and their relationships
- **File System Verification**: Validates file existence across multiple directory structures

### 3. Database Integrity Monitoring
- **Health Scoring**: Calculates integrity scores (0-100) based on data consistency
- **Issue Detection**: Automatically identifies data persistence problems
- **Recommendations**: Provides actionable suggestions for resolving issues
- **Integration**: Built into the existing System Health dashboard

### 4. Web Interface Integration
- **Admin Dashboard**: Dedicated Database Maintenance page accessible from System Health
- **One-Click Operations**: Execute cleanup operations through the web interface
- **Progress Tracking**: Real-time feedback on maintenance operations
- **Backup Integration**: Automatic backup creation before destructive operations

## Architecture

### Core Components

#### DatabaseMaintenanceService (`app/services/database_maintenance.py`)
- **Primary Service**: Main service class for all maintenance operations
- **Atomic Operations**: Provides atomic delete functionality with transaction handling
- **Integrity Monitoring**: Detects and reports on database integrity issues
- **Space Reclamation**: ChromaDB optimization and space reclamation
- **Backup Management**: Creates and manages database backups

#### Health Monitor Integration (`app/utils/health_monitor.py`)
- **Integrity Metrics**: Adds database integrity monitoring to system health
- **Scoring System**: Calculates integrity scores and health impact
- **Alert Integration**: Incorporates integrity issues into overall health status

#### Admin Routes (`app/routes/admin.py`)
- **Web Interface**: Provides HTTP endpoints for maintenance operations
- **API Endpoints**: RESTful APIs for database maintenance functions
- **Security**: Requires admin permissions and function-level access control

#### Templates (`app/templates/admin_database_maintenance.html`)
- **User Interface**: Comprehensive dashboard for database maintenance
- **Real-time Updates**: Auto-refreshing status and progress tracking
- **Interactive Operations**: One-click buttons for maintenance tasks

## Usage Guide

### Accessing the Database Maintenance Dashboard

1. **Navigate to System Health**: Go to `/admin/health` in the admin panel
2. **Click Database Maintenance**: Use the green "Database Maintenance" button
3. **Review Status**: Check the database integrity status cards
4. **Execute Operations**: Use the maintenance action buttons as needed

### Available Operations

#### 1. Cleanup Orphaned Records
- **Purpose**: Remove database records for files that no longer exist
- **When to Use**: When orphaned PDF records are detected
- **Process**: 
  - Creates automatic backup
  - Uses atomic transactions
  - Deletes related records across all tables
  - Reclaims ChromaDB space
  - Provides detailed results

#### 2. Reclaim ChromaDB Space
- **Purpose**: Optimize ChromaDB storage by reclaiming deleted space
- **When to Use**: When ChromaDB is large but contains few embeddings
- **Process**:
  - Runs VACUUM operation on ChromaDB
  - Forces WAL checkpoint
  - Optimizes database structure
  - Reports space reclaimed

#### 3. Rebuild ChromaDB
- **Purpose**: Completely rebuild ChromaDB from scratch for maximum optimization
- **When to Use**: For databases larger than 10MB or severe fragmentation
- **Process**:
  - Creates backup of existing ChromaDB
  - Rebuilds database with only valid documents
  - Reclaims maximum space
  - Provides rollback on failure

### Programmatic Usage

#### Using the Maintenance Service
```python
from app.services.database_maintenance import get_maintenance_service

# Get service instance
service = get_maintenance_service()

# Check database integrity
integrity_status = service.get_database_integrity_status()
print(f"Orphaned PDFs: {integrity_status.orphaned_pdfs}")

# Detect orphaned records
orphaned_records = service.detect_orphaned_records()

# Cleanup orphaned records
result = service.cleanup_orphaned_records(create_backup=True)
if result.success:
    print(f"Cleaned up {result.records_deleted['pdf_documents']} PDF records")
```

#### Using Atomic Delete Operations
```python
from app.utils.helpers import delete_file

# Use atomic delete (recommended)
success, message = delete_file("category", "filename.pdf", use_atomic=True)

# Or use the service directly
from app.services.database_maintenance import get_maintenance_service
service = get_maintenance_service()
result = service.atomic_delete_pdf("category", "filename.pdf")
```

## API Endpoints

### Database Integrity Status
- **Endpoint**: `GET /admin/health/api/database-integrity`
- **Purpose**: Get current database integrity status
- **Response**: JSON with integrity metrics and recommendations

### Cleanup Orphaned Records
- **Endpoint**: `POST /admin/health/api/cleanup-orphaned-records`
- **Purpose**: Execute orphaned records cleanup
- **Parameters**: `create_backup` (boolean, default: true)
- **Response**: JSON with cleanup results and statistics

### Reclaim ChromaDB Space
- **Endpoint**: `POST /admin/health/api/reclaim-chroma-space`
- **Purpose**: Optimize ChromaDB storage
- **Response**: JSON with space reclamation results

### Rebuild ChromaDB
- **Endpoint**: `POST /admin/health/api/rebuild-chroma-database`
- **Purpose**: Completely rebuild ChromaDB
- **Response**: JSON with rebuild results and statistics

## Monitoring and Alerts

### Health Integration
The database maintenance system is integrated into the existing health monitoring:

- **Health Score Impact**: Database integrity issues reduce overall health score
- **Issue Detection**: Orphaned records and ChromaDB issues are reported as health problems
- **Recommendations**: Specific maintenance actions are suggested in health reports
- **Auto-refresh**: Database integrity status updates automatically every 30 seconds

### Integrity Scoring
Database integrity is scored from 0-100 based on:
- **Orphaned PDFs**: -5 points per orphaned PDF (max -50 points)
- **Orphaned Submissions**: -3 points per orphaned submission (max -30 points)
- **Empty ChromaDB**: -20 points for large but empty ChromaDB files

## Best Practices

### Prevention
1. **Use Atomic Deletes**: Always use `delete_file()` with `use_atomic=True`
2. **Regular Monitoring**: Check database integrity through the health dashboard
3. **Backup Before Maintenance**: Always create backups before major operations
4. **Monitor ChromaDB Size**: Watch for unusual growth in ChromaDB files

### Maintenance Schedule
1. **Daily**: Monitor database integrity status
2. **Weekly**: Review orphaned records and cleanup if needed
3. **Monthly**: Consider ChromaDB optimization for large databases
4. **As Needed**: Rebuild ChromaDB for maximum space reclamation

### Troubleshooting
1. **Orphaned Records**: Use the cleanup operation from the web interface
2. **Large ChromaDB**: Try space reclamation first, then rebuild if needed
3. **Failed Operations**: Check logs and ensure database permissions are correct
4. **Backup Issues**: Verify disk space and write permissions in backup directory

## Security Considerations

- **Admin Access Required**: All maintenance operations require admin privileges
- **Function Permissions**: Uses function-level permission checking
- **Backup Creation**: Automatic backups before destructive operations
- **Transaction Safety**: All operations use database transactions for safety
- **Error Handling**: Comprehensive error handling with rollback mechanisms

## Performance Impact

- **Minimal Runtime Impact**: Operations are designed to be non-blocking
- **Background Processing**: Large operations can run in the background
- **Progress Tracking**: Real-time feedback on operation progress
- **Optimized Queries**: Efficient database queries to minimize performance impact

## Future Enhancements

- **Scheduled Maintenance**: Automatic cleanup operations on a schedule
- **Advanced Analytics**: Detailed reporting on database health trends
- **Notification System**: Email/SMS alerts for critical integrity issues
- **Bulk Operations**: Enhanced support for large-scale maintenance tasks
