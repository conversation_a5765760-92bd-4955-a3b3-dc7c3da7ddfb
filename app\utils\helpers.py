import os
import logging
import shutil
import hashlib
import time
import random
import json
import sqlite3
from app.services.vector_db import get_vector_db
from werkzeug.utils import secure_filename
from datetime import datetime
from app.utils.content_db import get_db_connection, get_pdf_by_original_filename
from app.utils.database import delete_pdf_locations
import spacy
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the directory where this script is located
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# Go up to the project root
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(BASE_DIR)))

# Environment variables
TEMP_FOLDER = os.getenv("TEMP_FOLDER", os.path.join(PROJECT_ROOT, "data", "temp"))
CHROMA_PATH = os.getenv("CHROMA_PATH", "./data/chroma/chroma")

# Load ScispaCy CRAFT model at module level
try:
    _scispacy_nlp = spacy.load("en_ner_craft_md")
except Exception as e:
    _scispacy_nlp = None
    logging.warning(f"ScispaCy CRAFT model not loaded: {e}")

def safe_remove_file(file_path, max_retries=5, initial_delay=0.1):
    """
    Safely remove a file with retry logic for handling locked files.

    Args:
        file_path: Path to the file to remove
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds

    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(file_path):
        return True

    retry_count = 0
    retry_delay = initial_delay

    while retry_count <= max_retries:
        try:
            os.remove(file_path)
            logger.info(f"Successfully removed file: {file_path}")
            return True
        except PermissionError as e:
            # File is locked by another process
            retry_count += 1
            if retry_count <= max_retries:
                logger.warning(f"File {file_path} is locked. Retrying in {retry_delay:.2f}s ({retry_count}/{max_retries})")
                time.sleep(retry_delay)
                # Exponential backoff with jitter
                retry_delay = retry_delay * 2 * (0.5 + random.random())
            else:
                logger.error(f"Failed to remove file {file_path} after {max_retries} attempts: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"Error removing file {file_path}: {str(e)}")
            return False

    return False

def safe_remove_directory(dir_path, max_retries=5, initial_delay=0.1):
    """
    Safely remove a directory with retry logic for handling locked files.

    Args:
        dir_path: Path to the directory to remove
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds

    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(dir_path) or not os.path.isdir(dir_path):
        return True

    retry_count = 0
    retry_delay = initial_delay

    while retry_count <= max_retries:
        try:
            shutil.rmtree(dir_path)
            logger.info(f"Successfully removed directory: {dir_path}")
            return True
        except (PermissionError, OSError) as e:
            # Directory or a file within it is locked by another process
            retry_count += 1
            if retry_count <= max_retries:
                logger.warning(f"Directory {dir_path} has locked files. Retrying in {retry_delay:.2f}s ({retry_count}/{max_retries})")
                time.sleep(retry_delay)
                # Exponential backoff with jitter
                retry_delay = retry_delay * 2 * (0.5 + random.random())
            else:
                logger.error(f"Failed to remove directory {dir_path} after {max_retries} attempts: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"Error removing directory {dir_path}: {str(e)}")
            return False

    return False

def allowed_file(filename: str) -> bool:
    return filename.lower().endswith(".pdf")

def delete_file(category: str, filename: str, use_atomic: bool = True):
    """
    Delete a file and all its associated resources (images, tables, vector embeddings).
    This function handles both the hierarchical directory structure and any legacy paths.

    Args:
        category: The category the file belongs to
        filename: The filename to delete
        use_atomic: Whether to use atomic delete operations (recommended)

    Returns:
        Tuple of (success, message)
    """
    # Use the new atomic delete service by default for better reliability
    if use_atomic:
        try:
            from app.services.database_maintenance import get_maintenance_service
            maintenance_service = get_maintenance_service()
            result = maintenance_service.atomic_delete_pdf(category, filename)

            if result.success:
                message_parts = []
                if result.records_deleted['pdf_documents'] > 0:
                    message_parts.append(f"database records")
                if result.space_reclaimed_mb > 0:
                    message_parts.append(f"reclaimed {result.space_reclaimed_mb:.2f}MB space")
                if result.warnings:
                    message_parts.extend(result.warnings)

                message = f"File {filename} deleted successfully"
                if message_parts:
                    message += f" ({', '.join(message_parts)})"

                return True, message
            else:
                error_msg = f"Failed to delete file {filename}: {'; '.join(result.errors)}"
                if result.warnings:
                    error_msg += f" (Warnings: {'; '.join(result.warnings)})"
                return False, error_msg

        except Exception as e:
            logger.warning(f"Atomic delete failed, falling back to legacy method: {str(e)}")
            # Fall back to legacy delete method
            pass
    try:
        resources_deleted = []
        pdf_name_base = os.path.splitext(filename)[0]
        
        # Define paths to check for the file and its resources
        category_paths = [
            os.path.join(TEMP_FOLDER, category),  # New simplified structure
            os.path.join(TEMP_FOLDER, "_temp", category)  # Legacy path where existing data is
        ]

        for category_path in category_paths:
            if not os.path.exists(category_path):
                continue

            logger.info(f"Checking for files in: {category_path}")

            # Check for the new directory structure first
            pdf_dir = os.path.join(category_path, pdf_name_base)

            # If the PDF directory exists in the new structure, delete the entire directory
            if os.path.exists(pdf_dir) and os.path.isdir(pdf_dir):
                # Delete the entire PDF directory (including the PDF file, images, tables, and thumbnails)
                if safe_remove_directory(pdf_dir):
                    resources_deleted.append("PDF directory with all resources (including thumbnails)")
                    logger.info(f"Deleted PDF directory with all resources: {pdf_dir}")
                else:
                    # If directory removal fails, try to delete individual files
                    logger.warning(f"Failed to remove entire directory {pdf_dir}, attempting to delete individual files")

                    # Try to delete the PDF file first
                    pdf_file_path = os.path.join(pdf_dir, filename)
                    if os.path.exists(pdf_file_path) and safe_remove_file(pdf_file_path):
                        resources_deleted.append("PDF file")

                    # Try to delete images directory
                    images_dir = os.path.join(pdf_dir, "pdf_images")
                    if os.path.exists(images_dir) and safe_remove_directory(images_dir):
                        resources_deleted.append("PDF images")

                    # Try to delete tables directory
                    tables_dir = os.path.join(pdf_dir, "pdf_tables")
                    if os.path.exists(tables_dir) and safe_remove_directory(tables_dir):
                        resources_deleted.append("PDF tables")
            else:
                # Check for PDF file directly in category directory (new simplified structure)
                file_path = os.path.join(category_path, filename)
                if os.path.exists(file_path) and safe_remove_file(file_path):
                    resources_deleted.append("PDF file")
                    logger.info(f"Deleted PDF file: {file_path}")

                # Check for associated directory
                pdf_dir = os.path.join(category_path, pdf_name_base)
                if os.path.exists(pdf_dir) and os.path.isdir(pdf_dir):
                    if safe_remove_directory(pdf_dir):
                        resources_deleted.append("PDF resources directory")
                        logger.info(f"Deleted PDF resources directory: {pdf_dir}")

        # Delete from vector database
        try:
            from app.services.unified_vector_db import get_global_chroma_client
            client = get_global_chroma_client()
            collection = client.get_collection(name="unified_collection")
            
            # Delete by filename and original_filename
            try:
                collection.delete(where={"filename": filename})
                logger.info(f"Deleted vector embeddings for filename: {filename}")
                resources_deleted.append("vector embeddings")
            except Exception as e:
                logger.warning(f"Failed to delete vector embeddings by filename: {str(e)}")

            try:
                collection.delete(where={"original_filename": filename})
                logger.info(f"Deleted vector embeddings for original_filename: {filename}")
                resources_deleted.append("vector embeddings")
            except Exception as e:
                logger.warning(f"Failed to delete vector embeddings by original_filename: {str(e)}")

        except Exception as e:
            logger.error(f"Failed to delete from vector database: {str(e)}")

        # Delete database records
        try:
            from app.utils.database import delete_pdf_document_records
            deleted_count = delete_pdf_document_records(category, filename)
            if deleted_count > 0:
                resources_deleted.append(f"{deleted_count} database records")
                logger.info(f"Deleted {deleted_count} database records for {filename}")
        except Exception as e:
            logger.error(f"Failed to delete database records: {str(e)}")

        # Delete location data
        try:
            from app.utils.database import delete_pdf_locations
            if delete_pdf_locations(filename, category):
                resources_deleted.append("location data")
                logger.info(f"Deleted location data for {filename}")
        except Exception as e:
            logger.error(f"Failed to delete location data: {str(e)}")

        if resources_deleted:
            message = f"Deleted {filename} and {', '.join(resources_deleted)}"
            logger.info(message)
            return True, message
        else:
            message = f"No files found to delete for {filename}"
            logger.warning(message)
            return False, message

    except Exception as e:
        error_msg = f"Error deleting file {filename}: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

def list_categories():
    """Return a list of category names from available data sources."""
    categories = set()
    
    # Check the temp directory for all categories
    temp_categories_path = os.path.join(TEMP_FOLDER)
    if os.path.exists(temp_categories_path):
        for item in os.listdir(temp_categories_path):
            item_path = os.path.join(temp_categories_path, item)
            if os.path.isdir(item_path) and item not in ['_temp', 'profile_pics', 'vision_cache', 'pdf_images', 'pdf_tables']:
                # Include all categories, not just those with files
                categories.add(item)
    
    # Check the legacy temp directory as a fallback
    legacy_temp_path = os.path.join(TEMP_FOLDER, "_temp")
    if os.path.exists(legacy_temp_path):
        for item in os.listdir(legacy_temp_path):
            item_path = os.path.join(legacy_temp_path, item)
            if os.path.isdir(item_path) and item not in ['profile_pics', 'vision_cache']:
                # Include all categories, not just those with files
                categories.add(item)
    
    # Check the legacy chroma directory as another fallback
    if os.path.exists(CHROMA_PATH):
        legacy_categories = [d for d in os.listdir(CHROMA_PATH) if os.path.isdir(os.path.join(CHROMA_PATH, d))]
        categories.update(legacy_categories)
    
    return sorted(list(categories))

def calculate_file_hash(file_obj):
    """
    Calculate SHA-256 hash of a file object.

    Args:
        file_obj: A file-like object (must support seek, read)

    Returns:
        str: Hexadecimal digest of the file hash
    """
    try:
        # Save the current position
        current_position = file_obj.tell()

        # Reset to beginning of file
        file_obj.seek(0)

        # Calculate hash
        file_hash = hashlib.sha256()
        chunk_size = 65536  # 64KB chunks

        while True:
            data = file_obj.read(chunk_size)
            if not data:
                break
            file_hash.update(data)

        # Restore the original position
        file_obj.seek(current_position)

        return file_hash.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating file hash: {str(e)}")
        return None

def check_duplicate_pdf(file_obj, category):
    """
    Check if a PDF file already exists by filename or content hash.

    Args:
        file_obj: The file object to check
        category: The category to check in

    Returns:
        tuple: (is_duplicate, duplicate_info)
            - is_duplicate (bool): True if a duplicate exists
            - duplicate_info (dict): Information about the duplicate
    """
    try:
        original_filename = secure_filename(file_obj.filename)
        logger.info(f"Checking for duplicates: filename='{original_filename}', category='{category}'")

        # 1. Check for duplicate by original_filename in the database
        # This is the most reliable and primary way to check for duplicates.
        existing_pdf = get_pdf_by_original_filename(original_filename, category)
        if existing_pdf:
            logger.info(f"Duplicate found by filename: '{original_filename}' exists in category '{category}' with filename '{existing_pdf['filename']}'.")
            return True, {
                'type': 'filename_match',
                'category': category,
                'filename': existing_pdf['filename'],  # Return the filename for deletion
                'original_filename': original_filename
            }

        # 2. Check if file exists directly in the filesystem
        from app.utils.helpers import TEMP_FOLDER
        category_path = os.path.join(TEMP_FOLDER, category)
        file_path = os.path.join(category_path, original_filename)
        
        if os.path.exists(file_path):
            logger.info(f"Duplicate found by direct file existence: '{original_filename}' exists in category '{category}'")
            return True, {
                'type': 'file_exists',
                'category': category,
                'filename': original_filename,
                'original_filename': original_filename
            }

        # 3. If no filename match, check by content hash (fallback)
        # This catches files that may have been renamed but are otherwise identical.
        file_hash = calculate_file_hash(file_obj)
        if not file_hash:
            logger.warning(f"Could not calculate hash for {original_filename}, skipping content check.")
            return False, None

        # To check by hash, we must scan the filesystem.
        # This is less efficient, which is why we check the DB by name first.
        category_paths = [
            os.path.join(TEMP_FOLDER, category),
            os.path.join(TEMP_FOLDER, "_temp", category)
        ]
        
        logger.info(f"Scanning category paths for content hash matches: {category_paths}")
        
        for category_path in category_paths:
            if not os.path.exists(category_path):
                logger.info(f"Category path does not exist: {category_path}")
                continue
                
            try:
                for existing_filename in os.listdir(category_path):
                    if existing_filename.lower().endswith('.pdf'):
                        file_path = os.path.join(category_path, existing_filename)
                        try:
                            with open(file_path, 'rb') as f:
                                existing_hash = calculate_file_hash(f)
                                if existing_hash == file_hash:
                                    logger.info(f"Duplicate found by content hash: {original_filename} matches {existing_filename}")
                                    return True, {
                                        'type': 'content_match',
                                        'category': category,
                                        'filename': existing_filename,
                                        'original_filename': original_filename
                                    }
                        except Exception as e:
                            logger.warning(f"Error checking file hash for {file_path}: {e}")
                            continue
            except Exception as e:
                logger.warning(f"Error scanning directory {category_path}: {e}")
                continue
        
        logger.info(f"No duplicates found for '{original_filename}' in category '{category}'")
        return False, None
    except Exception as e:
        logger.error(f"Error checking for duplicate PDF: {e}")
        return False, None

def create_category(category_name: str) -> bool:
    """
    Create a new category with all necessary directories.
    
    Args:
        category_name: Name of the category to create
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create main files directory in data/temp
        dest_dir = os.path.join(TEMP_FOLDER, category_name)
        os.makedirs(dest_dir, exist_ok=True)
        logger.info(f"Created files directory for category: {category_name}")
        
        # Create images directory
        IMAGES_FOLDER = os.getenv("IMAGES_FOLDER", "./data/temp/pdf_images")
        images_dir = os.path.join(IMAGES_FOLDER, category_name)
        os.makedirs(images_dir, exist_ok=True)
        logger.info(f"Created images directory for category: {category_name}")
        
        # Create tables directory
        TABLES_FOLDER = os.getenv("TABLES_FOLDER", "./data/temp/pdf_tables")
        tables_dir = os.path.join(TABLES_FOLDER, category_name)
        os.makedirs(tables_dir, exist_ok=True)
        logger.info(f"Created tables directory for category: {category_name}")
        
        logger.info(f"Category '{category_name}' created successfully with all directories")
        return True
        
    except Exception as e:
        logger.error(f"Error creating category '{category_name}': {e}")
        return False

def delete_category_and_files(category_name):
    """
    Delete a category and all its associated files and database entries.
    
    Args:
        category_name (str): Name of the category to delete
        
    Returns:
        dict: Result with success status and message
    """
    try:
        logger.info(f"Starting deletion of category: {category_name}")
        
        # 1. Delete files from temp directory
        temp_category_path = os.path.join(TEMP_FOLDER, category_name)
        if os.path.exists(temp_category_path):
            import shutil
            shutil.rmtree(temp_category_path)
            logger.info(f"Deleted temp directory: {temp_category_path}")
        
        # 2. Delete files from legacy temp directory
        legacy_temp_category_path = os.path.join(TEMP_FOLDER, "_temp", category_name)
        if os.path.exists(legacy_temp_category_path):
            import shutil
            shutil.rmtree(legacy_temp_category_path)
            logger.info(f"Deleted legacy temp directory: {legacy_temp_category_path}")
        
        # 3. Delete from database with proper foreign key handling
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Disable foreign key constraints temporarily
        cursor.execute("PRAGMA foreign_keys=OFF")
        
        try:
            # Delete in the correct order to avoid foreign key constraint violations
            
            # First, delete from chat_analytics (references chat_history)
            cursor.execute("DELETE FROM chat_analytics WHERE category = ?", (category_name,))
            analytics_deleted = cursor.rowcount
            
            # Delete from chat_history
            cursor.execute("DELETE FROM chat_history WHERE category = ?", (category_name,))
            chat_deleted = cursor.rowcount
            
            # Delete from scraped_pages
            cursor.execute("DELETE FROM scraped_pages WHERE category = ?", (category_name,))
            pages_deleted = cursor.rowcount
            
            # Delete from pdf_documents (this might have foreign key references)
            cursor.execute("DELETE FROM pdf_documents WHERE category = ?", (category_name,))
            pdf_deleted = cursor.rowcount
            
            # Delete from source_urls that are only used by this category
            # First, get all source_url_ids that are used by PDFs in this category
            cursor.execute("SELECT DISTINCT source_url_id FROM pdf_documents WHERE category = ? AND source_url_id IS NOT NULL", (category_name,))
            source_url_ids = [row[0] for row in cursor.fetchall()]
            
            # Delete source_urls that are only used by this category
            source_urls_deleted = 0
            for source_url_id in source_url_ids:
                # Check if this source_url is used by any other categories
                cursor.execute("SELECT COUNT(*) FROM pdf_documents WHERE source_url_id = ? AND category != ?", (source_url_id, category_name))
                other_usage = cursor.fetchone()[0]
                
                if other_usage == 0:
                    # This source_url is only used by the category being deleted
                    cursor.execute("DELETE FROM source_urls WHERE id = ?", (source_url_id,))
                    source_urls_deleted += cursor.rowcount
            
            # Re-enable foreign key constraints
            cursor.execute("PRAGMA foreign_keys=ON")
            
            conn.commit()
            
            logger.info(f"Database cleanup completed: {pdf_deleted} PDFs, {pages_deleted} pages, {chat_deleted} chats, {analytics_deleted} analytics, {source_urls_deleted} source URLs deleted")
            
            return {
                "success": True,
                "message": f"Category '{category_name}' and all associated files deleted successfully",
                "deleted_items": {
                    "pdfs": pdf_deleted,
                    "pages": pages_deleted,
                    "chats": chat_deleted,
                    "analytics": analytics_deleted,
                    "source_urls": source_urls_deleted
                }
            }
            
        except Exception as db_error:
            # Re-enable foreign key constraints even if there's an error
            cursor.execute("PRAGMA foreign_keys=ON")
            conn.rollback()
            raise db_error
            
    except Exception as e:
        logger.error(f"Error deleting category {category_name}: {str(e)}")
        return {
            "success": False,
            "message": f"Error deleting category: {str(e)}"
        }
    finally:
        if 'conn' in locals():
            conn.close()

def italicize_scientific_names(text):
    """
    Detect scientific names in text and wrap them in <i>...</i> tags using ScispaCy (CRAFT model).
    """
    if not _scispacy_nlp or not text:
        return text
    doc = _scispacy_nlp(text)
    # Collect entities to replace, in reverse order to avoid messing up indices
    entities = [ent for ent in doc.ents if ent.label_ in ("GENE_OR_GENE_PRODUCT", "SPECIES", "ORGANISM", "TAXON")]
    new_text = text
    offset = 0
    for ent in sorted(entities, key=lambda e: e.start_char, reverse=True):
        start, end = ent.start_char, ent.end_char
        entity_text = new_text[start:end]
        # Avoid double-wrapping
        if not (entity_text.startswith('<i>') and entity_text.endswith('</i>')):
            new_text = new_text[:start] + '<i>' + entity_text + '</i>' + new_text[end:]
    return new_text

def vacuum_chromadb():
    """
    Perform enhanced VACUUM operation on ChromaDB to reclaim unused space after deletions.
    This includes WAL checkpoint forcing and additional optimizations to prevent accumulation.
    
    Returns:
        Tuple of (success, size_before, size_after, space_reclaimed)
    """
    try:
        # Path to the unified ChromaDB SQLite file
        db_path = "./data/unified_chroma/chroma.sqlite3"
        
        if not os.path.exists(db_path):
            logger.warning(f"ChromaDB file not found at {db_path}")
            return False, 0, 0, 0
        
        # Get size before VACUUM (including WAL files)
        size_before = os.path.getsize(db_path)
        
        # Check for WAL files
        wal_path = db_path + "-wal"
        shm_path = db_path + "-shm"
        wal_size_before = os.path.getsize(wal_path) if os.path.exists(wal_path) else 0
        shm_size_before = os.path.getsize(shm_path) if os.path.exists(shm_path) else 0
        total_size_before = size_before + wal_size_before + shm_size_before
        
        logger.info(f"Before optimization - Main: {size_before/1024/1024:.2f} MB, WAL: {wal_size_before/1024/1024:.2f} MB, SHM: {shm_size_before/1024/1024:.2f} MB")
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check journal mode
        cursor.execute("PRAGMA journal_mode")
        journal_mode = cursor.fetchone()[0]
        logger.info(f"Database journal mode: {journal_mode}")
        
        # Force WAL checkpoint if in WAL mode
        if journal_mode.lower() == 'wal':
            logger.info("Forcing WAL checkpoint...")
            cursor.execute("PRAGMA wal_checkpoint(TRUNCATE)")
            checkpoint_result = cursor.fetchone()
            logger.info(f"WAL checkpoint result: busy={checkpoint_result[0]}, log_size={checkpoint_result[1]}, checkpointed={checkpoint_result[2]}")
        
        # Get database info before optimization
        cursor.execute("PRAGMA page_count")
        pages_before = cursor.fetchone()[0]
        cursor.execute("PRAGMA freelist_count")
        free_pages_before = cursor.fetchone()[0]
        
        logger.info(f"Before VACUUM - Pages: {pages_before}, Free pages: {free_pages_before}")
        
        # Perform enhanced VACUUM operation
        logger.info("Running VACUUM operation...")
        cursor.execute("VACUUM")
        
        # Run ANALYZE to update statistics
        logger.info("Running ANALYZE operation...")
        cursor.execute("ANALYZE")
        
        # Additional optimizations
        logger.info("Running PRAGMA optimize...")
        cursor.execute("PRAGMA optimize")
        
        # Get database info after optimization
        cursor.execute("PRAGMA page_count")
        pages_after = cursor.fetchone()[0]
        cursor.execute("PRAGMA freelist_count")
        free_pages_after = cursor.fetchone()[0]
        
        logger.info(f"After VACUUM - Pages: {pages_after}, Free pages: {free_pages_after}")
        
        # Force final WAL checkpoint if in WAL mode
        if journal_mode.lower() == 'wal':
            logger.info("Final WAL checkpoint...")
            cursor.execute("PRAGMA wal_checkpoint(TRUNCATE)")
            final_checkpoint = cursor.fetchone()
            logger.info(f"Final checkpoint result: busy={final_checkpoint[0]}, log_size={final_checkpoint[1]}, checkpointed={final_checkpoint[2]}")
        
        conn.close()
        
        # Get size after VACUUM (including WAL files)
        size_after = os.path.getsize(db_path)
        wal_size_after = os.path.getsize(wal_path) if os.path.exists(wal_path) else 0
        shm_size_after = os.path.getsize(shm_path) if os.path.exists(shm_path) else 0
        total_size_after = size_after + wal_size_after + shm_size_after
        
        space_reclaimed = total_size_before - total_size_after
        
        logger.info(f"After optimization - Main: {size_after/1024/1024:.2f} MB, WAL: {wal_size_after/1024/1024:.2f} MB, SHM: {shm_size_after/1024/1024:.2f} MB")
        logger.info(f"Enhanced VACUUM completed: {total_size_before/1024/1024:.2f} MB → {total_size_after/1024/1024:.2f} MB (reclaimed {space_reclaimed/1024/1024:.2f} MB)")
        
        return True, total_size_before / (1024 * 1024), total_size_after / (1024 * 1024), space_reclaimed / (1024 * 1024)
        
    except Exception as e:
        logger.error(f"Enhanced VACUUM operation failed: {str(e)}")
        return False, 0, 0, 0

def get_files_in_category(category):
    """
    Return a list of all original_filename values for PDFs in the given category from the pdf_documents table.
    Args:
        category (str): The category to list files for.
    Returns:
        list: List of original_filename strings (or empty list if none).
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT original_filename FROM pdf_documents WHERE category = ?", (category,))
        rows = cursor.fetchall()
        return [row[0] for row in rows if row[0]]
    except Exception as e:
        logger.error(f"Error in get_files_in_category: {e}")
        return []
    finally:
        if 'conn' in locals():
            conn.close()