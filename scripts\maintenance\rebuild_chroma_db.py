#!/usr/bin/env python3
"""
Rebuild ChromaDB to reclaim space after cleanup

This script completely rebuilds the ChromaDB to reclaim space when VACUUM doesn't work.
Since all embeddings have been deleted, we can safely recreate the database.

Usage:
    python scripts/maintenance/rebuild_chroma_db.py [--dry-run]
"""

import os
import sys
import shutil
import logging
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'chroma_rebuild_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def rebuild_chroma_db(dry_run=False):
    """Rebuild ChromaDB to reclaim space."""
    chroma_dir = Path("data/unified_chroma")
    chroma_db_path = chroma_dir / "chroma.sqlite3"
    
    if not chroma_db_path.exists():
        logger.warning("ChromaDB file not found")
        return True
    
    # Get current size
    size_before = chroma_db_path.stat().st_size
    logger.info(f"Current ChromaDB size: {size_before/1024/1024:.2f} MB")
    
    if dry_run:
        logger.info("DRY RUN: Would rebuild ChromaDB to reclaim space")
        return True
    
    try:
        # Create backup
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = Path(f"backups/chroma_rebuild_{timestamp}")
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Backup the entire chroma directory
        backup_chroma_dir = backup_dir / "unified_chroma"
        shutil.copytree(chroma_dir, backup_chroma_dir)
        logger.info(f"Backed up ChromaDB to {backup_dir}")
        
        # Remove the old ChromaDB directory
        shutil.rmtree(chroma_dir)
        logger.info("Removed old ChromaDB directory")
        
        # Recreate the directory structure
        chroma_dir.mkdir(parents=True, exist_ok=True)
        logger.info("Created fresh ChromaDB directory")
        
        # Initialize a new ChromaDB
        try:
            # Add the project root to the Python path
            project_root = Path(__file__).parent.parent.parent
            sys.path.insert(0, str(project_root))
            
            from app.services.unified_vector_db import get_unified_vector_db
            
            # Initialize the new database
            db = get_unified_vector_db()
            stats = db.get_collection_stats()
            logger.info(f"Initialized new ChromaDB: {stats}")
            
            # Get new size
            if chroma_db_path.exists():
                size_after = chroma_db_path.stat().st_size
                space_reclaimed = size_before - size_after
                logger.info(f"New ChromaDB size: {size_after/1024/1024:.2f} MB")
                logger.info(f"Space reclaimed: {space_reclaimed/1024/1024:.2f} MB")
            else:
                logger.info("New ChromaDB not yet created (will be created on first use)")
                logger.info(f"Space reclaimed: {size_before/1024/1024:.2f} MB")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing new ChromaDB: {str(e)}")
            # Restore from backup
            logger.info("Restoring from backup...")
            shutil.rmtree(chroma_dir)
            shutil.copytree(backup_chroma_dir, chroma_dir)
            logger.info("Restored ChromaDB from backup")
            return False
            
    except Exception as e:
        logger.error(f"Error rebuilding ChromaDB: {str(e)}")
        return False

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Rebuild ChromaDB to reclaim space")
    parser.add_argument("--dry-run", action="store_true", help="Preview what would be done without actually doing it")
    
    args = parser.parse_args()
    
    if args.dry_run:
        logger.info("Running in DRY RUN mode - no actual changes will be made")
    
    logger.info("Starting ChromaDB rebuild...")
    
    success = rebuild_chroma_db(dry_run=args.dry_run)
    
    if success:
        logger.info("ChromaDB rebuild completed successfully!")
        sys.exit(0)
    else:
        logger.error("ChromaDB rebuild failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
