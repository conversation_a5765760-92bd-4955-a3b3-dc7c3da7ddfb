{% extends "admin_base.html" %}

{% block title %}Database Maintenance{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-database fa-fw me-2"></i>Database Maintenance
        </h1>
        <div>
            <a href="{{ url_for('admin.health_dashboard') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-arrow-left fa-fw me-1"></i>Back to Health Dashboard
            </a>
        </div>
    </div>

    <!-- Database Integrity Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shield-alt fa-fw me-2"></i>Database Integrity Status
                    </h6>
                    <button onclick="refreshIntegrityStatus()" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-sync-alt fa-fw me-1"></i>Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- PDF Documents -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-left-primary h-100">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                PDF Documents
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ integrity_status.total_pdfs }}
                                            </div>
                                            {% if integrity_status.orphaned_pdfs > 0 %}
                                            <div class="text-xs text-danger">
                                                {{ integrity_status.orphaned_pdfs }} orphaned
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-file-pdf fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Submissions -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Form Submissions
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ integrity_status.total_submissions }}
                                            </div>
                                            {% if integrity_status.orphaned_submissions > 0 %}
                                            <div class="text-xs text-danger">
                                                {{ integrity_status.orphaned_submissions }} orphaned
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ChromaDB Size -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-left-warning h-100">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                ChromaDB Size
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ "%.2f"|format(integrity_status.chroma_size_mb) }} MB
                                            </div>
                                            <div class="text-xs text-muted">
                                                {{ integrity_status.chroma_embeddings }} embeddings
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-hdd fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Health Score -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-left-{% if integrity_status.issues|length == 0 %}success{% elif integrity_status.issues|length <= 2 %}warning{% else %}danger{% endif %} h-100">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-{% if integrity_status.issues|length == 0 %}success{% elif integrity_status.issues|length <= 2 %}warning{% else %}danger{% endif %} text-uppercase mb-1">
                                                Health Status
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {% if integrity_status.issues|length == 0 %}
                                                    Healthy
                                                {% elif integrity_status.issues|length <= 2 %}
                                                    Warning
                                                {% else %}
                                                    Critical
                                                {% endif %}
                                            </div>
                                            <div class="text-xs text-muted">
                                                {{ integrity_status.issues|length }} issues
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-heartbeat fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Issues and Recommendations -->
                    {% if integrity_status.issues or integrity_status.recommendations %}
                    <div class="row mt-3">
                        {% if integrity_status.issues %}
                        <div class="col-md-6">
                            <h6 class="text-danger">
                                <i class="fas fa-exclamation-triangle fa-fw me-1"></i>Issues Found
                            </h6>
                            <ul class="list-unstyled">
                                {% for issue in integrity_status.issues %}
                                <li class="text-danger mb-1">
                                    <i class="fas fa-circle fa-xs me-2"></i>{{ issue }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        {% if integrity_status.recommendations %}
                        <div class="col-md-6">
                            <h6 class="text-info">
                                <i class="fas fa-lightbulb fa-fw me-1"></i>Recommendations
                            </h6>
                            <ul class="list-unstyled">
                                {% for recommendation in integrity_status.recommendations %}
                                <li class="text-info mb-1">
                                    <i class="fas fa-circle fa-xs me-2"></i>{{ recommendation }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tools fa-fw me-2"></i>Maintenance Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Cleanup Orphaned Records -->
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-danger h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-danger">
                                        <i class="fas fa-trash-alt fa-fw me-2"></i>Cleanup Orphaned Records
                                    </h6>
                                    <p class="card-text text-muted">
                                        Remove database records for files that no longer exist on the file system.
                                        This prevents data inconsistency and UI display issues.
                                    </p>
                                    {% if integrity_status.orphaned_pdfs > 0 %}
                                    <div class="alert alert-warning alert-sm">
                                        <strong>{{ integrity_status.orphaned_pdfs }}</strong> orphaned records detected
                                    </div>
                                    {% endif %}
                                    <button onclick="cleanupOrphanedRecords()" class="btn btn-danger btn-sm" 
                                            {% if integrity_status.orphaned_pdfs == 0 %}disabled{% endif %}>
                                        <i class="fas fa-broom fa-fw me-1"></i>
                                        {% if integrity_status.orphaned_pdfs > 0 %}
                                            Cleanup {{ integrity_status.orphaned_pdfs }} Records
                                        {% else %}
                                            No Cleanup Needed
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Reclaim ChromaDB Space -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-warning h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-warning">
                                        <i class="fas fa-compress-alt fa-fw me-2"></i>Reclaim ChromaDB Space
                                    </h6>
                                    <p class="card-text text-muted">
                                        Optimize ChromaDB storage by reclaiming space from deleted embeddings.
                                        Quick optimization method.
                                    </p>
                                    {% if integrity_status.chroma_size_mb > 1 and integrity_status.chroma_embeddings == 0 %}
                                    <div class="alert alert-warning alert-sm">
                                        <strong>{{ "%.1f"|format(integrity_status.chroma_size_mb) }} MB</strong> can be reclaimed
                                    </div>
                                    {% endif %}
                                    <button onclick="reclaimChromaSpace()" class="btn btn-warning btn-sm">
                                        <i class="fas fa-magic fa-fw me-1"></i>Reclaim Space
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Rebuild ChromaDB -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-sync-alt fa-fw me-2"></i>Rebuild ChromaDB
                                    </h6>
                                    <p class="card-text text-muted">
                                        Completely rebuild ChromaDB from scratch for maximum space reclamation.
                                        More thorough than space reclamation.
                                    </p>
                                    {% if integrity_status.chroma_size_mb > 10 %}
                                    <div class="alert alert-info alert-sm">
                                        Recommended for databases larger than 10MB
                                    </div>
                                    {% endif %}
                                    <button onclick="rebuildChromaDatabase()" class="btn btn-info btn-sm">
                                        <i class="fas fa-hammer fa-fw me-1"></i>Rebuild Database
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orphaned Records Details -->
    {% if orphaned_records %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list fa-fw me-2"></i>Orphaned Records Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Filename</th>
                                    <th>Original Filename</th>
                                    <th>Category</th>
                                    <th>Form ID</th>
                                    <th>Related Records</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in orphaned_records %}
                                <tr>
                                    <td>{{ record.id }}</td>
                                    <td><code>{{ record.filename }}</code></td>
                                    <td>{{ record.original_filename }}</td>
                                    <td><span class="badge bg-secondary">{{ record.category }}</span></td>
                                    <td>
                                        {% if record.form_id %}
                                            <span class="badge bg-info">{{ record.form_id }}</span>
                                        {% else %}
                                            <span class="text-muted">None</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if record.related_records %}
                                            {% for table, count in record.related_records.items() %}
                                                {% if count > 0 %}
                                                    <span class="badge bg-warning me-1">{{ table }}: {{ count }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Operation Status Modal -->
    <div class="modal fade" id="operationModal" tabindex="-1" aria-labelledby="operationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="operationModalLabel">Maintenance Operation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="operationStatus">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Operation in progress...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh integrity status every 30 seconds
let refreshInterval;

document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});

function startAutoRefresh() {
    refreshInterval = setInterval(refreshIntegrityStatus, 30000);
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

function refreshIntegrityStatus() {
    fetch('/admin/health/api/database-integrity')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the page with new data
                location.reload();
            }
        })
        .catch(error => console.error('Error refreshing integrity status:', error));
}

function cleanupOrphanedRecords() {
    const modal = new bootstrap.Modal(document.getElementById('operationModal'));
    document.getElementById('operationModalLabel').textContent = 'Cleanup Orphaned Records';
    document.getElementById('operationStatus').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Cleaning up orphaned records...</p>
        </div>
    `;
    modal.show();

    fetch('/admin/health/api/cleanup-orphaned-records', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            create_backup: true
        })
    })
    .then(response => response.json())
    .then(data => {
        let statusHtml = '';
        if (data.success) {
            statusHtml = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Cleanup Completed Successfully</h6>
                    <ul class="mb-0">
                        <li>PDF documents deleted: ${data.records_deleted.pdf_documents}</li>
                        <li>Form submissions deleted: ${data.records_deleted.form_submissions}</li>
                        <li>Cover images deleted: ${data.records_deleted.cover_images}</li>
                        <li>Location sources deleted: ${data.records_deleted.location_sources}</li>
                        <li>Space reclaimed: ${data.space_reclaimed_mb.toFixed(2)} MB</li>
                        <li>Duration: ${data.duration_seconds.toFixed(2)} seconds</li>
                    </ul>
                </div>
            `;
            if (data.warnings && data.warnings.length > 0) {
                statusHtml += `
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Warnings</h6>
                        <ul class="mb-0">
                            ${data.warnings.map(warning => `<li>${warning}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
        } else {
            statusHtml = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>Cleanup Failed</h6>
                    <p>${data.error}</p>
                </div>
            `;
        }
        document.getElementById('operationStatus').innerHTML = statusHtml;
        
        // Refresh the page after a short delay
        setTimeout(() => {
            location.reload();
        }, 3000);
    })
    .catch(error => {
        document.getElementById('operationStatus').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-times-circle me-2"></i>Operation Failed</h6>
                <p>Error: ${error.message}</p>
            </div>
        `;
    });
}

function reclaimChromaSpace() {
    const modal = new bootstrap.Modal(document.getElementById('operationModal'));
    document.getElementById('operationModalLabel').textContent = 'Reclaim ChromaDB Space';
    document.getElementById('operationStatus').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Reclaiming ChromaDB space...</p>
        </div>
    `;
    modal.show();

    fetch('/admin/health/api/reclaim-chroma-space', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        let statusHtml = '';
        if (data.success) {
            statusHtml = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Space Reclamation Completed</h6>
                    <p>Space reclaimed: <strong>${data.space_reclaimed_mb.toFixed(2)} MB</strong></p>
                </div>
            `;
        } else {
            statusHtml = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>Space Reclamation Failed</h6>
                    <p>${data.error}</p>
                </div>
            `;
        }
        document.getElementById('operationStatus').innerHTML = statusHtml;

        // Refresh the page after a short delay
        setTimeout(() => {
            location.reload();
        }, 3000);
    })
    .catch(error => {
        document.getElementById('operationStatus').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-times-circle me-2"></i>Operation Failed</h6>
                <p>Error: ${error.message}</p>
            </div>
        `;
    });
}

function rebuildChromaDatabase() {
    if (!confirm('Are you sure you want to rebuild the ChromaDB? This will recreate the entire database and may take some time.')) {
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('operationModal'));
    document.getElementById('operationModalLabel').textContent = 'Rebuild ChromaDB';
    document.getElementById('operationStatus').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Rebuilding ChromaDB from scratch...</p>
            <small class="text-muted">This may take several minutes</small>
        </div>
    `;
    modal.show();

    fetch('/admin/health/api/rebuild-chroma-database', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        let statusHtml = '';
        if (data.success) {
            statusHtml = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>ChromaDB Rebuild Completed</h6>
                    <ul class="mb-0">
                        <li>Documents processed: ${data.records_deleted.chroma_embeddings || 0}</li>
                        <li>Space reclaimed: ${data.space_reclaimed_mb.toFixed(2)} MB</li>
                        <li>Duration: ${data.duration_seconds.toFixed(2)} seconds</li>
                    </ul>
                </div>
            `;
            if (data.warnings && data.warnings.length > 0) {
                statusHtml += `
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Warnings</h6>
                        <ul class="mb-0">
                            ${data.warnings.map(warning => `<li>${warning}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
        } else {
            statusHtml = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>ChromaDB Rebuild Failed</h6>
                    <p>${data.error}</p>
                </div>
            `;
        }
        document.getElementById('operationStatus').innerHTML = statusHtml;

        // Refresh the page after a short delay
        setTimeout(() => {
            location.reload();
        }, 5000);
    })
    .catch(error => {
        document.getElementById('operationStatus').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-times-circle me-2"></i>Operation Failed</h6>
                <p>Error: ${error.message}</p>
            </div>
        `;
    });
}
</script>
{% endblock %}
