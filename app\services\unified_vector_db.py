"""
Unified Vector Database Service
Provides a single ChromaDB instance with metadata filtering for better performance.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
from langchain.schema import Document
import chromadb
from chromadb.config import Settings

logger = logging.getLogger(__name__)

class UnifiedVectorDB:
    """
    Unified vector database service that consolidates all collections into a single database
    with metadata filtering for category separation.
    """
    
    def __init__(self):
        self.persist_directory = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        self.collection_name = "unified_collection"
        self.embedding_model = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")
        self._db = None
        self._embedding_function = None
        self._chroma_client = None
        
        # Ensure directory exists
        os.makedirs(self.persist_directory, exist_ok=True)
    
    def _get_embedding_function(self):
        """Get or create the embedding function."""
        if self._embedding_function is None:
            try:
                self._embedding_function = OllamaEmbeddings(model=self.embedding_model)
                logger.info(f"Initialized embedding function with model: {self.embedding_model}")
            except Exception as e:
                logger.error(f"Failed to initialize embedding function: {str(e)}")
                raise
        return self._embedding_function
    
    def _get_chroma_client(self):
        """Get the global ChromaDB client instance."""
        return get_global_chroma_client()
    
    def _get_db(self):
        """Get or create the ChromaDB instance."""
        if self._db is None:
            try:
                embed_fn = self._get_embedding_function()
                self._db = Chroma(
                    collection_name=self.collection_name,
                    persist_directory=self.persist_directory,
                    embedding_function=embed_fn
                )
                logger.info(f"Initialized unified ChromaDB at: {self.persist_directory}")
            except Exception as e:
                logger.error(f"Failed to initialize ChromaDB: {str(e)}")
                raise
        return self._db
    
    def add_documents(self, documents: List[Document], category: str, **kwargs):
        """
        Add documents to the unified database with category metadata.
        
        Args:
            documents: List of documents to add
            category: Category for the documents
            **kwargs: Additional metadata
        """
        try:
            db = self._get_db()
            
            # Prepare metadata with category
            metadatas = []
            for doc in documents:
                metadata = doc.metadata.copy() if hasattr(doc, 'metadata') else {}
                metadata['category'] = category
                metadata.update(kwargs)
                metadatas.append(metadata)
            
            # Add documents
            db.add_documents(documents, metadatas=metadatas)
            logger.info(f"Added {len(documents)} documents to category: {category}")
            
        except Exception as e:
            logger.error(f"Failed to add documents: {str(e)}")
            raise
    
    def similarity_search(self, query: str, category: Optional[str] = None, 
                         k: int = 10, **kwargs) -> List[Document]:
        """
        Perform similarity search with optional category filtering.
        
        Args:
            query: Search query
            category: Optional category filter
            k: Number of results to return
            **kwargs: Additional search parameters
            
        Returns:
            List of similar documents
        """
        try:
            db = self._get_db()
            
            # Prepare filter
            filter_dict = {}
            if category:
                filter_dict['category'] = category
            
            # Perform search
            results = db.similarity_search(
                query, 
                k=k,
                filter=filter_dict if filter_dict else None,
                **kwargs
            )
            
            logger.info(f"Found {len(results)} documents for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search: {str(e)}")
            raise
    
    def similarity_search_with_score(self, query: str, category: Optional[str] = None,
                                   k: int = 10, **kwargs) -> List[tuple]:
        """
        Perform similarity search with scores.
        
        Args:
            query: Search query
            category: Optional category filter
            k: Number of results to return
            **kwargs: Additional search parameters
            
        Returns:
            List of (document, score) tuples
        """
        try:
            db = self._get_db()
            
            # Prepare filter
            filter_dict = {}
            if category:
                filter_dict['category'] = category
            
            # Perform search
            results = db.similarity_search_with_score(
                query,
                k=k,
                filter=filter_dict if filter_dict else None,
                **kwargs
            )
            
            logger.info(f"Found {len(results)} documents with scores for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search with scores: {str(e)}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the unified collection.
        
        Returns:
            Dictionary with collection statistics
        """
        try:
            # Use the singleton ChromaDB client
            client = self._get_chroma_client()
            collection = client.get_collection(name=self.collection_name)
            
            # Get basic stats
            count = collection.count()
            
            # Get category distribution
            results = collection.get(limit=10000)  # Sample for stats
            categories = {}
            for metadata in results.get('metadatas', []):
                if metadata and 'category' in metadata:
                    cat = metadata['category']
                    categories[cat] = categories.get(cat, 0) + 1
            
            stats = {
                'total_documents': count,
                'categories': categories,
                'collection_name': self.collection_name,
                'persist_directory': self.persist_directory
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {str(e)}")
            return {'error': str(e)}
    
    def delete_documents(self, category: Optional[str] = None, 
                        filter_dict: Optional[Dict] = None):
        """
        Delete documents from the collection.
        
        Args:
            category: Optional category filter
            filter_dict: Additional filter criteria
        """
        try:
            db = self._get_db()
            
            # Prepare filter
            if filter_dict is None:
                filter_dict = {}
            if category:
                filter_dict['category'] = category
            
            # Delete documents
            if filter_dict:
                db.delete(filter=filter_dict)
                logger.info(f"Deleted documents with filter: {filter_dict}")
            else:
                logger.warning("No filter provided for deletion. Use with caution.")
                
        except Exception as e:
            logger.error(f"Failed to delete documents: {str(e)}")
            raise
    
    def update_document_metadata(self, document_id: str, metadata: Dict[str, Any]):
        """
        Update metadata for a specific document.
        
        Args:
            document_id: ID of the document to update
            metadata: New metadata
        """
        try:
            # Use the singleton ChromaDB client
            client = self._get_chroma_client()
            collection = client.get_collection(name=self.collection_name)
            
            # Update metadata
            collection.update(ids=[document_id], metadatas=[metadata])
            logger.info(f"Updated metadata for document: {document_id}")
            
        except Exception as e:
            logger.error(f"Failed to update document metadata: {str(e)}")
            raise
    
    def get_document_by_id(self, document_id: str) -> Optional[Document]:
        """
        Get a specific document by ID.
        
        Args:
            document_id: ID of the document
            
        Returns:
            Document if found, None otherwise
        """
        try:
            # Use the singleton ChromaDB client
            client = self._get_chroma_client()
            collection = client.get_collection(name=self.collection_name)
            
            # Get document
            results = collection.get(ids=[document_id])
            
            if results['ids']:
                # Convert to Document format
                doc = Document(
                    page_content=results['documents'][0],
                    metadata=results['metadatas'][0] or {}
                )
                return doc
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to get document by ID: {str(e)}")
            return None
    
    def optimize_collection(self):
        """
        Optimize the collection for better performance.
        """
        try:
            # Use the singleton ChromaDB client
            client = self._get_chroma_client()
            collection = client.get_collection(name=self.collection_name)
            
            # ChromaDB automatically optimizes, but we can trigger some operations
            logger.info("Collection optimization completed")
            
        except Exception as e:
            logger.error(f"Failed to optimize collection: {str(e)}")
            raise

# Global instance for easy access
_unified_db = None
_chroma_client = None

def get_unified_vector_db() -> UnifiedVectorDB:
    """
    Get the global unified vector database instance.
    
    Returns:
        UnifiedVectorDB instance
    """
    global _unified_db
    if _unified_db is None:
        _unified_db = UnifiedVectorDB()
    return _unified_db

def get_global_chroma_client():
    """
    Get the global ChromaDB client instance.
    This ensures only one ChromaDB client exists across the entire application.
    
    Returns:
        ChromaDB client instance
    """
    global _chroma_client
    if _chroma_client is None:
        import chromadb
        persist_directory = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        _chroma_client = chromadb.PersistentClient(path=persist_directory)
        logger.info(f"Initialized global ChromaDB client at: {persist_directory}")
    return _chroma_client

def get_vector_db_by_category(category: str) -> UnifiedVectorDB:
    """
    Get vector database instance for a specific category.
    This maintains compatibility with existing code while using the unified database.
    
    Args:
        category: Category name (used for metadata filtering)
        
    Returns:
        UnifiedVectorDB instance
    """
    # Return the unified instance - category filtering is handled in search methods
    return get_unified_vector_db() 