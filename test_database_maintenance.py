#!/usr/bin/env python3
"""
Test script for database maintenance functionality

This script validates the new database maintenance features including:
- Orphaned record detection
- Database integrity monitoring
- Atomic delete operations
- ChromaDB space reclamation
"""

import sys
import os
sys.path.append('.')

def test_database_maintenance():
    """Test database maintenance functionality."""
    print("🧪 Testing Database Maintenance Functionality")
    print("=" * 50)
    
    try:
        # Test 1: Import and instantiate service
        print("\n1. Testing service imports and instantiation...")
        from app.services.database_maintenance import get_maintenance_service
        service = get_maintenance_service()
        print("   ✓ Database maintenance service imported and instantiated")
        
        # Test 2: Database integrity status
        print("\n2. Testing database integrity status...")
        integrity_status = service.get_database_integrity_status()
        print(f"   ✓ Total PDFs: {integrity_status.total_pdfs}")
        print(f"   ✓ Orphaned PDFs: {integrity_status.orphaned_pdfs}")
        print(f"   ✓ Total submissions: {integrity_status.total_submissions}")
        print(f"   ✓ ChromaDB size: {integrity_status.chroma_size_mb:.2f} MB")
        print(f"   ✓ ChromaDB embeddings: {integrity_status.chroma_embeddings}")
        print(f"   ✓ Issues found: {len(integrity_status.issues)}")
        print(f"   ✓ Recommendations: {len(integrity_status.recommendations)}")
        
        # Test 3: Orphaned record detection
        print("\n3. Testing orphaned record detection...")
        orphaned_records = service.detect_orphaned_records()
        print(f"   ✓ Found {len(orphaned_records)} orphaned records")
        
        if orphaned_records:
            print("   📋 Orphaned records details:")
            for record in orphaned_records[:5]:  # Show first 5
                print(f"      - ID {record.id}: {record.filename} ({record.category})")
        
        # Test 4: Health monitor integration
        print("\n4. Testing health monitor integration...")
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        db_integrity = monitor.get_database_integrity_metrics()
        if db_integrity:
            print(f"   ✓ Database integrity score: {db_integrity.integrity_score}/100")
            print(f"   ✓ Issues count: {db_integrity.issues_count}")
        else:
            print("   ! Database integrity metrics not available")
        
        health_status = monitor.check_health_status()
        print(f"   ✓ Overall health status: {health_status.status}")
        print(f"   ✓ Health score: {health_status.score}/100")
        print(f"   ✓ Issues: {len(health_status.issues)}")
        print(f"   ✓ Recommendations: {len(health_status.recommendations)}")
        
        # Test 5: Database connections
        print("\n5. Testing database connections...")
        try:
            with service.get_db_connection(service.main_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM pdf_documents")
                pdf_count = cursor.fetchone()[0]
                print(f"   ✓ Main database connection successful ({pdf_count} PDFs)")
        except Exception as e:
            print(f"   ! Main database connection failed: {str(e)}")
        
        if os.path.exists(service.chroma_db_path):
            try:
                with service.get_db_connection(service.chroma_db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    print(f"   ✓ ChromaDB connection successful ({len(tables)} tables)")
            except Exception as e:
                print(f"   ! ChromaDB connection failed: {str(e)}")
        else:
            print("   ! ChromaDB file not found")
        
        # Test 6: Backup functionality
        print("\n6. Testing backup functionality...")
        try:
            backup_success = service._create_backup()
            if backup_success:
                print("   ✓ Backup creation successful")
            else:
                print("   ! Backup creation failed")
        except Exception as e:
            print(f"   ! Backup test failed: {str(e)}")
        
        # Test 7: File existence checking
        print("\n7. Testing file existence checking...")
        test_categories = ['test_category', 'documents', 'reports']
        for category in test_categories:
            exists = service._check_file_exists('nonexistent_file.pdf', category)
            print(f"   ✓ File existence check for {category}: {exists}")
        
        print("\n" + "=" * 50)
        print("🎉 Database Maintenance Test Summary")
        print("=" * 50)
        
        # Summary
        summary_items = [
            f"Total PDFs in database: {integrity_status.total_pdfs}",
            f"Orphaned records found: {integrity_status.orphaned_pdfs}",
            f"ChromaDB size: {integrity_status.chroma_size_mb:.2f} MB",
            f"Database integrity score: {db_integrity.integrity_score if db_integrity else 'N/A'}/100",
            f"Overall health status: {health_status.status}",
            f"Health score: {health_status.score}/100"
        ]
        
        for item in summary_items:
            print(f"   📊 {item}")
        
        if integrity_status.issues:
            print("\n   ⚠️  Issues detected:")
            for issue in integrity_status.issues:
                print(f"      - {issue}")
        
        if integrity_status.recommendations:
            print("\n   💡 Recommendations:")
            for rec in integrity_status.recommendations:
                print(f"      - {rec}")
        
        if integrity_status.orphaned_pdfs == 0 and len(integrity_status.issues) == 0:
            print("\n   ✅ Database is healthy - no maintenance required!")
        else:
            print("\n   🔧 Database maintenance recommended")
            print("      Use the Database Maintenance dashboard to resolve issues")
        
        print("\n✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_admin_routes():
    """Test admin route availability."""
    print("\n🌐 Testing Admin Route Integration")
    print("=" * 50)
    
    try:
        # Test route imports
        from app.routes.admin import admin_bp
        print("   ✓ Admin blueprint imported successfully")
        
        # Check if our new routes are registered
        routes = []
        for rule in admin_bp.url_map.iter_rules():
            if 'database-maintenance' in rule.rule or 'database-integrity' in rule.rule:
                routes.append(rule.rule)
        
        expected_routes = [
            '/health/database-maintenance',
            '/health/api/database-integrity',
            '/health/api/cleanup-orphaned-records',
            '/health/api/reclaim-chroma-space',
            '/health/api/rebuild-chroma-database'
        ]
        
        print(f"   ✓ Found {len(routes)} database maintenance routes")
        for route in routes:
            print(f"      - {route}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Admin route test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Database Maintenance Tests")
    print("=" * 60)
    
    # Run tests
    maintenance_test = test_database_maintenance()
    admin_test = test_admin_routes()
    
    print("\n" + "=" * 60)
    print("📋 Final Test Results")
    print("=" * 60)
    print(f"   Database Maintenance: {'✅ PASS' if maintenance_test else '❌ FAIL'}")
    print(f"   Admin Route Integration: {'✅ PASS' if admin_test else '❌ FAIL'}")
    
    if maintenance_test and admin_test:
        print("\n🎉 All tests passed! Database maintenance system is ready.")
        print("\n📖 Next steps:")
        print("   1. Access the Database Maintenance dashboard at /admin/health/database-maintenance")
        print("   2. Monitor database integrity through the System Health dashboard")
        print("   3. Use atomic delete operations for better data consistency")
        print("   4. Run cleanup operations when orphaned records are detected")
    else:
        print("\n⚠️  Some tests failed. Please review the errors above.")
        sys.exit(1)
