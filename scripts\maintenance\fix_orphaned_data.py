#!/usr/bin/env python3
"""
Fix Orphaned Data Cleanup Script

This script addresses the data persistence issue where:
1. Physical PDF files were deleted
2. Vector embeddings were cleared
3. Database records remain orphaned
4. ChromaDB file size wasn't reclaimed

Usage:
    python scripts/maintenance/fix_orphaned_data.py [--dry-run] [--backup]
"""

import os
import sys
import sqlite3
import shutil
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'orphaned_data_cleanup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OrphanedDataCleaner:
    """Clean up orphaned database records and reclaim storage space."""
    
    def __init__(self, dry_run=False, create_backup=True):
        self.dry_run = dry_run
        self.create_backup = create_backup
        self.main_db_path = "erdb_main.db"
        self.chroma_db_path = "data/unified_chroma/chroma.sqlite3"
        self.temp_dir = Path("data/temp")
        
        # Statistics
        self.stats = {
            'orphaned_pdfs': 0,
            'orphaned_submissions': 0,
            'orphaned_cover_images': 0,
            'orphaned_location_sources': 0,
            'chroma_size_before': 0,
            'chroma_size_after': 0
        }
    
    def backup_databases(self):
        """Create backup of databases before cleanup."""
        if not self.create_backup:
            return True
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = Path(f"backups/orphaned_data_fix_{timestamp}")
        
        try:
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup main database
            if os.path.exists(self.main_db_path):
                shutil.copy2(self.main_db_path, backup_dir / "erdb_main.db")
                logger.info(f"Backed up main database to {backup_dir}")
            
            # Backup ChromaDB
            if os.path.exists(self.chroma_db_path):
                chroma_backup_dir = backup_dir / "unified_chroma"
                chroma_backup_dir.mkdir(exist_ok=True)
                shutil.copy2(self.chroma_db_path, chroma_backup_dir / "chroma.sqlite3")
                logger.info(f"Backed up ChromaDB to {backup_dir}")
            
            logger.info(f"Backup created at: {backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {str(e)}")
            return False
    
    def get_orphaned_pdfs(self):
        """Find PDF records whose files no longer exist."""
        try:
            conn = sqlite3.connect(self.main_db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, filename, original_filename, category, form_id
                FROM pdf_documents
            ''')
            
            all_pdfs = cursor.fetchall()
            orphaned_pdfs = []
            
            for pdf_id, filename, original_filename, category, form_id in all_pdfs:
                # Check if the file exists
                file_path = self.temp_dir / category / filename
                if not file_path.exists():
                    orphaned_pdfs.append({
                        'id': pdf_id,
                        'filename': filename,
                        'original_filename': original_filename,
                        'category': category,
                        'form_id': form_id
                    })
            
            conn.close()
            return orphaned_pdfs
            
        except Exception as e:
            logger.error(f"Error finding orphaned PDFs: {str(e)}")
            return []
    
    def clean_orphaned_records(self, orphaned_pdfs):
        """Remove orphaned records from all related tables."""
        if not orphaned_pdfs:
            logger.info("No orphaned PDFs found")
            return True
        
        if self.dry_run:
            logger.info(f"DRY RUN: Would delete {len(orphaned_pdfs)} orphaned PDF records")
            for pdf in orphaned_pdfs:
                logger.info(f"  - {pdf['original_filename']} (ID: {pdf['id']})")
            return True
        
        try:
            conn = sqlite3.connect(self.main_db_path)
            cursor = conn.cursor()
            
            pdf_ids = [pdf['id'] for pdf in orphaned_pdfs]
            pdf_ids_str = ','.join(map(str, pdf_ids))
            
            # Delete related records first (to avoid foreign key issues)
            
            # 1. Delete form submissions
            cursor.execute(f'''
                DELETE FROM form_submissions 
                WHERE pdf_document_id IN ({pdf_ids_str})
            ''')
            submissions_deleted = cursor.rowcount
            logger.info(f"Deleted {submissions_deleted} orphaned form submissions")
            
            # 2. Delete cover images
            cursor.execute(f'''
                DELETE FROM cover_images 
                WHERE pdf_document_id IN ({pdf_ids_str})
            ''')
            images_deleted = cursor.rowcount
            logger.info(f"Deleted {images_deleted} orphaned cover images")
            
            # 3. Delete location sources (where source_type is 'pdf_document' and source_id matches)
            cursor.execute(f'''
                DELETE FROM location_sources
                WHERE source_type = 'pdf_document' AND source_id IN ({pdf_ids_str})
            ''')
            locations_deleted = cursor.rowcount
            logger.info(f"Deleted {locations_deleted} orphaned location sources")
            
            # 4. Finally delete the PDF documents
            cursor.execute(f'''
                DELETE FROM pdf_documents 
                WHERE id IN ({pdf_ids_str})
            ''')
            pdfs_deleted = cursor.rowcount
            logger.info(f"Deleted {pdfs_deleted} orphaned PDF documents")
            
            conn.commit()
            conn.close()
            
            # Update statistics
            self.stats['orphaned_pdfs'] = pdfs_deleted
            self.stats['orphaned_submissions'] = submissions_deleted
            self.stats['orphaned_cover_images'] = images_deleted
            self.stats['orphaned_location_sources'] = locations_deleted
            
            return True
            
        except Exception as e:
            logger.error(f"Error cleaning orphaned records: {str(e)}")
            return False
    
    def vacuum_chroma_db(self):
        """Reclaim space in ChromaDB by running VACUUM."""
        if not os.path.exists(self.chroma_db_path):
            logger.warning("ChromaDB file not found")
            return True
        
        try:
            # Get size before
            size_before = os.path.getsize(self.chroma_db_path)
            self.stats['chroma_size_before'] = size_before
            
            if self.dry_run:
                logger.info(f"DRY RUN: Would VACUUM ChromaDB (current size: {size_before/1024/1024:.2f} MB)")
                return True
            
            logger.info(f"ChromaDB size before VACUUM: {size_before/1024/1024:.2f} MB")
            
            # Connect and VACUUM
            conn = sqlite3.connect(self.chroma_db_path)
            cursor = conn.cursor()
            
            # Force WAL checkpoint
            cursor.execute("PRAGMA wal_checkpoint(FULL)")
            
            # Run VACUUM
            cursor.execute("VACUUM")
            
            # Additional optimizations
            cursor.execute("PRAGMA optimize")
            
            conn.close()
            
            # Get size after
            size_after = os.path.getsize(self.chroma_db_path)
            self.stats['chroma_size_after'] = size_after
            space_reclaimed = size_before - size_after
            
            logger.info(f"ChromaDB size after VACUUM: {size_after/1024/1024:.2f} MB")
            logger.info(f"Space reclaimed: {space_reclaimed/1024/1024:.2f} MB")
            
            return True
            
        except Exception as e:
            logger.error(f"Error vacuuming ChromaDB: {str(e)}")
            return False
    
    def verify_cleanup(self):
        """Verify that cleanup was successful."""
        try:
            conn = sqlite3.connect(self.main_db_path)
            cursor = conn.cursor()
            
            # Check remaining gated PDFs
            cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE form_id IS NOT NULL')
            remaining_gated = cursor.fetchone()[0]
            
            # Check total PDFs
            cursor.execute('SELECT COUNT(*) FROM pdf_documents')
            total_pdfs = cursor.fetchone()[0]
            
            # Check form submissions
            cursor.execute('SELECT COUNT(*) FROM form_submissions')
            total_submissions = cursor.fetchone()[0]
            
            conn.close()
            
            logger.info("=== CLEANUP VERIFICATION ===")
            logger.info(f"Remaining gated PDFs: {remaining_gated}")
            logger.info(f"Total PDF records: {total_pdfs}")
            logger.info(f"Total form submissions: {total_submissions}")
            
            if remaining_gated == 0:
                logger.info("✅ SUCCESS: No orphaned gated PDFs remain")
                return True
            else:
                logger.warning(f"⚠️  WARNING: {remaining_gated} gated PDFs still exist")
                return False
                
        except Exception as e:
            logger.error(f"Error verifying cleanup: {str(e)}")
            return False
    
    def run_cleanup(self):
        """Run the complete orphaned data cleanup process."""
        logger.info("Starting orphaned data cleanup...")
        logger.info(f"Dry run mode: {self.dry_run}")
        
        # Step 1: Create backup
        if not self.backup_databases():
            logger.error("Backup failed. Aborting cleanup.")
            return False
        
        # Step 2: Find orphaned PDFs
        logger.info("Finding orphaned PDF records...")
        orphaned_pdfs = self.get_orphaned_pdfs()
        
        if not orphaned_pdfs:
            logger.info("No orphaned PDFs found. Proceeding to ChromaDB cleanup.")
        else:
            logger.info(f"Found {len(orphaned_pdfs)} orphaned PDF records")
            
            # Step 3: Clean orphaned records
            if not self.clean_orphaned_records(orphaned_pdfs):
                logger.error("Failed to clean orphaned records")
                return False
        
        # Step 4: VACUUM ChromaDB
        logger.info("Reclaiming ChromaDB storage space...")
        if not self.vacuum_chroma_db():
            logger.error("Failed to VACUUM ChromaDB")
            return False
        
        # Step 5: Verify cleanup
        if not self.dry_run:
            self.verify_cleanup()
        
        # Print summary
        self.print_summary()
        
        return True
    
    def print_summary(self):
        """Print cleanup summary."""
        logger.info("=== CLEANUP SUMMARY ===")
        logger.info(f"Orphaned PDFs deleted: {self.stats['orphaned_pdfs']}")
        logger.info(f"Orphaned form submissions deleted: {self.stats['orphaned_submissions']}")
        logger.info(f"Orphaned cover images deleted: {self.stats['orphaned_cover_images']}")
        logger.info(f"Orphaned location sources deleted: {self.stats['orphaned_location_sources']}")
        
        if self.stats['chroma_size_before'] > 0:
            size_before_mb = self.stats['chroma_size_before'] / 1024 / 1024
            size_after_mb = self.stats['chroma_size_after'] / 1024 / 1024
            space_reclaimed_mb = (self.stats['chroma_size_before'] - self.stats['chroma_size_after']) / 1024 / 1024
            
            logger.info(f"ChromaDB size before: {size_before_mb:.2f} MB")
            logger.info(f"ChromaDB size after: {size_after_mb:.2f} MB")
            logger.info(f"Space reclaimed: {space_reclaimed_mb:.2f} MB")

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Fix orphaned data after incomplete cleanup")
    parser.add_argument("--dry-run", action="store_true", help="Preview what would be cleaned without actually doing it")
    parser.add_argument("--no-backup", action="store_true", help="Skip creating backup (not recommended)")
    
    args = parser.parse_args()
    
    cleaner = OrphanedDataCleaner(
        dry_run=args.dry_run,
        create_backup=not args.no_backup
    )
    
    if args.dry_run:
        logger.info("Running in DRY RUN mode - no actual changes will be made")
    
    success = cleaner.run_cleanup()
    
    if success:
        logger.info("Orphaned data cleanup completed successfully!")
        sys.exit(0)
    else:
        logger.error("Orphaned data cleanup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
