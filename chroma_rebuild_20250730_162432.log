2025-07-30 16:24:32,585 - INFO - Starting ChromaDB rebuild...
2025-07-30 16:24:32,585 - INFO - Current ChromaDB size: 6.95 MB
2025-07-30 16:24:32,869 - INFO - Backed up ChromaDB to backups\chroma_rebuild_20250730_162432
2025-07-30 16:24:32,871 - INFO - Removed old ChromaDB directory
2025-07-30 16:24:32,872 - INFO - Created fresh ChromaDB directory
2025-07-30 16:24:34,469 - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-30 16:24:35,662 - ERROR - Failed to get collection stats: Collection [unified_collection] does not exists
2025-07-30 16:24:35,662 - INFO - Initialized new ChromaDB: {'error': 'Collection [unified_collection] does not exists'}
2025-07-30 16:24:35,663 - INFO - New ChromaDB size: 0.16 MB
2025-07-30 16:24:35,663 - INFO - Space reclaimed: 6.79 MB
2025-07-30 16:24:35,663 - INFO - ChromaDB rebuild completed successfully!
2025-07-30 16:24:36,138 - WARNING - Retrying (Retry(total=1, connect=1, read=2, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C929D2B0>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")': /batch/
2025-07-30 16:24:36,139 - WARNING - Retrying (Retry(total=0, connect=0, read=2, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B4F50>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")': /batch/
2025-07-30 16:24:36,225 - INFO - Backing off send_request(...) for 0.6s (requests.exceptions.ConnectionError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B51D0>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")))
2025-07-30 16:24:36,820 - WARNING - Retrying (Retry(total=1, connect=1, read=2, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B5810>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")': /batch/
2025-07-30 16:24:36,821 - WARNING - Retrying (Retry(total=0, connect=0, read=2, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B5BD0>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")': /batch/
2025-07-30 16:24:36,822 - INFO - Backing off send_request(...) for 1.4s (requests.exceptions.ConnectionError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B5E50>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")))
2025-07-30 16:24:38,179 - WARNING - Retrying (Retry(total=1, connect=1, read=2, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B6490>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")': /batch/
2025-07-30 16:24:38,179 - WARNING - Retrying (Retry(total=0, connect=0, read=2, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B6850>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")': /batch/
2025-07-30 16:24:38,180 - INFO - Backing off send_request(...) for 3.5s (requests.exceptions.ConnectionError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B6AD0>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")))
2025-07-30 16:24:41,649 - WARNING - Retrying (Retry(total=1, connect=1, read=2, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B6C10>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")': /batch/
2025-07-30 16:24:41,649 - WARNING - Retrying (Retry(total=0, connect=0, read=2, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B7250>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")': /batch/
2025-07-30 16:24:41,650 - ERROR - Giving up send_request(...) after 4 tries (requests.exceptions.ConnectionError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F1C92B74D0>: Failed to resolve 'us.i.posthog.com' ([Errno 11004] getaddrinfo failed)")))
