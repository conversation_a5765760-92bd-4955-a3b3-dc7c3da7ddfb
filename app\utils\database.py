import os
import sqlite3
import json
import logging
import time
from typing import Optional
import traceback

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

CHAT_DB_PATH = os.getenv("CHAT_DB_PATH", "./chat_history.db")
SCRAPED_DB_PATH = os.getenv("SCRAPED_DB_PATH", "./scraped_pages.db")

def init_db():
    try:
        # Initialize chat_history.db
        conn_chat = sqlite3.connect(CHAT_DB_PATH)
        cursor_chat = conn_chat.cursor()

        # Check if metadata column exists
        cursor_chat.execute("PRAGMA table_info(chat_history)")
        columns = cursor_chat.fetchall()
        column_names = [column[1] for column in columns]

        # Create table if it doesn't exist
        if not columns:
            cursor_chat.execute('''
                CREATE TABLE IF NOT EXISTS chat_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT,
                    question TEXT,
                    answer TEXT,
                    sources TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    images TEXT,
                    pdf_links TEXT,
                    metadata TEXT,
                    url_images TEXT,
                    pdf_images TEXT,
                    document_thumbnails TEXT,
                    client_name TEXT,
                    session_id TEXT,
                    session_start DATETIME,
                    session_end DATETIME
                )
            ''')
            logger.info("Created chat_history table with all columns including session management.")
        # Add metadata column if it doesn't exist
        elif 'metadata' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN metadata TEXT')
            logger.info("Added metadata column to existing chat_history table.")

        # Add session management columns if they don't exist
        if 'session_id' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN session_id TEXT')
            logger.info("Added session_id column to chat_history table.")

        if 'session_start' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN session_start DATETIME')
            logger.info("Added session_start column to chat_history table.")

        if 'session_end' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN session_end DATETIME')
            logger.info("Added session_end column to chat_history table.")

        # Add device fingerprint column if it doesn't exist
        if 'device_fingerprint' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN device_fingerprint TEXT')
            logger.info("Added device_fingerprint column to chat_history table.")

        # Add anti_hallucination_mode column if it doesn't exist
        if 'anti_hallucination_mode' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN anti_hallucination_mode TEXT')
            logger.info("Added anti_hallucination_mode column to chat_history table.")

        # Add model_name column if it doesn't exist
        if 'model_name' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN model_name TEXT')
            logger.info("Added model_name column to chat_history table.")

        # Add embedding_model column if it doesn't exist
        if 'embedding_model' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN embedding_model TEXT')
            logger.info("Added embedding_model column to chat_history table.")

        # Add vision_model column if it doesn't exist
        if 'vision_model' not in column_names:
            cursor_chat.execute('ALTER TABLE chat_history ADD COLUMN vision_model TEXT')
            logger.info("Added vision_model column to chat_history table.")

        # Check if chat_analytics table exists and get its columns
        cursor_chat.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='chat_analytics'")
        analytics_table_exists = cursor_chat.fetchone() is not None

        if analytics_table_exists:
            # Check existing columns in chat_analytics
            cursor_chat.execute("PRAGMA table_info(chat_analytics)")
            analytics_columns = cursor_chat.fetchall()
            analytics_column_names = [column[1] for column in analytics_columns]

            # Add missing columns if they don't exist
            if 'vision_model' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN vision_model TEXT')
                logger.info("Added vision_model column to chat_analytics table.")

            if 'vision_enabled' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN vision_enabled BOOLEAN')
                logger.info("Added vision_enabled column to chat_analytics table.")

            if 'images_filtered' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN images_filtered INTEGER')
                logger.info("Added images_filtered column to chat_analytics table.")

            if 'total_images_extracted' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN total_images_extracted INTEGER')
                logger.info("Added total_images_extracted column to chat_analytics table.")

            if 'filter_sensitivity' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN filter_sensitivity TEXT')
                logger.info("Added filter_sensitivity column to chat_analytics table.")

            if 'device_fingerprint' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN device_fingerprint TEXT')
                logger.info("Added device_fingerprint column to chat_analytics table.")

            # Add geolocation columns
            if 'ip_address' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN ip_address TEXT')
                logger.info("Added ip_address column to chat_analytics table.")

            if 'city' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN city TEXT')
                logger.info("Added city column to chat_analytics table.")

            if 'region' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN region TEXT')
                logger.info("Added region column to chat_analytics table.")

            if 'country' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN country TEXT')
                logger.info("Added country column to chat_analytics table.")

            if 'latitude' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN latitude REAL')
                logger.info("Added latitude column to chat_analytics table.")

            if 'longitude' not in analytics_column_names:
                cursor_chat.execute('ALTER TABLE chat_analytics ADD COLUMN longitude REAL')
                logger.info("Added longitude column to chat_analytics table.")
        else:
            # Create analytics table if it doesn't exist
            cursor_chat.execute('''
                CREATE TABLE IF NOT EXISTS chat_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    chat_id INTEGER,
                    category TEXT,
                    client_name TEXT,
                    question_length INTEGER,
                    answer_length INTEGER,
                    processing_time REAL,
                    source_count INTEGER,
                    image_count INTEGER,
                    token_count INTEGER,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    model_name TEXT,
                    embedding_model TEXT,
                    vision_model TEXT,
                    vision_enabled BOOLEAN,
                    images_filtered INTEGER,
                    total_images_extracted INTEGER,
                    filter_sensitivity TEXT,
                    hallucination_detected BOOLEAN,
                    device_fingerprint TEXT,
                    ip_address TEXT,
                    city TEXT,
                    region TEXT,
                    country TEXT,
                    latitude REAL,
                    longitude REAL,
                    FOREIGN KEY (chat_id) REFERENCES chat_history (id)
                )
            ''')
            logger.info("Created chat_analytics table with all columns.")

        conn_chat.commit()
        logger.info("Initialized chat_history database.")

        # Initialize scraped_pages.db
        conn_scraped = sqlite3.connect(SCRAPED_DB_PATH)
        cursor_scraped = conn_scraped.cursor()
        cursor_scraped.execute('''
            CREATE TABLE IF NOT EXISTS scraped_pages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT NOT NULL,
                url TEXT NOT NULL,
                filename TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn_scraped.commit()
        logger.info("Initialized scraped_pages database.")
    except sqlite3.Error as e:
        logger.error(f"Failed to initialize databases: {str(e)}")
        raise
    finally:
        if 'conn_chat' in locals():
            conn_chat.close()
        if 'conn_scraped' in locals():
            conn_scraped.close()

def save_scraped_page(category, url, filename):
    try:
        conn = sqlite3.connect(SCRAPED_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO scraped_pages (category, url, filename)
            VALUES (?, ?, ?)
        ''', (category, url, filename))
        conn.commit()
        logger.info(f"Saved scraped page for category: {category}, url: {url}, filename: {filename}")
    except sqlite3.Error as e:
        logger.error(f"Failed to save scraped page: {str(e)}")
        raise
    finally:
        conn.close()

def get_scraped_pages():
    try:
        conn = sqlite3.connect(SCRAPED_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT category, url, filename FROM scraped_pages')
        rows = cursor.fetchall()
        return [{"category": row[0], "url": row[1], "filename": row[2]} for row in rows]
    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve scraped pages: {str(e)}")
        return []
    finally:
        conn.close()

def delete_scraped_page(category, filename):
    try:
        conn = sqlite3.connect(SCRAPED_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('''
            DELETE FROM scraped_pages WHERE category = ? AND filename = ?
        ''', (category, filename))
        conn.commit()
        logger.info(f"Deleted scraped page for category: {category}, filename: {filename}")
    except sqlite3.Error as e:
        logger.error(f"Failed to delete scraped page: {str(e)}")
        raise
    finally:
        conn.close()

def delete_pdf_document_records(category, filename):
    """
    Delete PDF document records from the main database.
    This handles multiple filename patterns that might be associated with the same file.
    """
    try:
        # Use the main database path
        main_db_path = "./erdb_main.db"
        
        conn = sqlite3.connect(main_db_path)
        cursor = conn.cursor()
        
        # Delete records that match various filename patterns
        patterns_to_check = [
            filename,  # Exact match
            filename.replace('_non_ocr', ''),  # Remove _non_ocr suffix
            f"{filename}_non_ocr",  # Add _non_ocr suffix
        ]
        
        # Also check for base filename without any suffixes
        base_filename = filename
        if filename.endswith('_non_ocr.pdf'):
            base_filename = filename.replace('_non_ocr.pdf', '.pdf')
        elif filename.endswith('.pdf'):
            base_filename = filename
        
        patterns_to_check.extend([
            base_filename,
            f"{base_filename}_non_ocr"
        ])
        
        deleted_count = 0
        for pattern in set(patterns_to_check):  # Remove duplicates
            # Delete by filename and category
            cursor.execute('''
                DELETE FROM pdf_documents 
                WHERE category = ? AND (filename = ? OR original_filename = ?)
            ''', (category, pattern, pattern))
            deleted_count += cursor.rowcount
            
            # Also delete records that contain the pattern (for any variations)
            cursor.execute('''
                DELETE FROM pdf_documents 
                WHERE category = ? AND (filename LIKE ? OR original_filename LIKE ?)
            ''', (category, f'%{pattern}%', f'%{pattern}%'))
            deleted_count += cursor.rowcount
        
        conn.commit()
        conn.close()
        
        if deleted_count > 0:
            logger.info(f"Deleted {deleted_count} PDF document records for {filename} in category {category}")
        else:
            logger.warning(f"No PDF document records found for {filename} in category {category}")
        
        return deleted_count
        
    except Exception as e:
        logger.error(f"Error deleting PDF document records: {str(e)}")
        return 0

def save_chat_history(category, question, answer, sources, images, pdf_links, metadata=None, url_images=None, pdf_images=None, client_name=None, session_id=None, session_start=None, device_fingerprint=None, document_thumbnails=None, anti_hallucination_mode=None, model_name=None, embedding_model=None, vision_model=None):
    """
    Save chat history to the database.

    Returns:
        int: The ID of the inserted chat history entry, or None if an error occurred.
    """
    import os
    logger.error(f"Saving chat history to DB: {os.path.abspath(CHAT_DB_PATH)}")
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Print the columns at runtime for debugging
        cursor.execute("PRAGMA table_info(chat_history)")
        columns_info = cursor.fetchall()
        logger.error(f"chat_history columns at runtime: {[col[1] for col in columns_info]}")
        column_names = [col[1] for col in columns_info]

        # Build columns and values lists dynamically
        insert_columns = []
        insert_values = []
        # Always present columns
        if 'category' in column_names:
            insert_columns.append('category')
            insert_values.append(category)
        if 'question' in column_names:
            insert_columns.append('question')
            insert_values.append(question)
        if 'answer' in column_names:
            insert_columns.append('answer')
            insert_values.append(answer)
        if 'sources' in column_names:
            insert_columns.append('sources')
            insert_values.append(json.dumps(sources))
        if 'images' in column_names:
            insert_columns.append('images')
            insert_values.append(json.dumps(images))
        if 'pdf_links' in column_names:
            insert_columns.append('pdf_links')
            insert_values.append(json.dumps(pdf_links))
        if 'metadata' in column_names:
            insert_columns.append('metadata')
            insert_values.append(json.dumps(metadata) if metadata else None)
        if 'url_images' in column_names:
            insert_columns.append('url_images')
            insert_values.append(json.dumps(url_images) if url_images else None)
        if 'pdf_images' in column_names:
            insert_columns.append('pdf_images')
            insert_values.append(json.dumps(pdf_images) if pdf_images else None)
        if 'document_thumbnails' in column_names:
            insert_columns.append('document_thumbnails')
            insert_values.append(json.dumps(document_thumbnails) if document_thumbnails else None)
        if 'client_name' in column_names:
            insert_columns.append('client_name')
            insert_values.append(client_name)
        if 'session_id' in column_names:
            insert_columns.append('session_id')
            insert_values.append(session_id)
        if 'session_start' in column_names:
            insert_columns.append('session_start')
            insert_values.append(session_start)
        if 'device_fingerprint' in column_names:
            insert_columns.append('device_fingerprint')
            insert_values.append(device_fingerprint)
        if 'anti_hallucination_mode' in column_names:
            insert_columns.append('anti_hallucination_mode')
            insert_values.append(anti_hallucination_mode)
        if 'model_name' in column_names:
            insert_columns.append('model_name')
            insert_values.append(model_name)
        if 'embedding_model' in column_names:
            insert_columns.append('embedding_model')
            insert_values.append(embedding_model)
        if 'vision_model' in column_names:
            insert_columns.append('vision_model')
            insert_values.append(vision_model)

        # Compose the SQL
        placeholders = ', '.join(['?'] * len(insert_columns))
        sql = f"INSERT INTO chat_history ({', '.join(insert_columns)}) VALUES ({placeholders})"
        cursor.execute(sql, insert_values)

        chat_id = cursor.lastrowid
        conn.commit()
        logger.info(f"Saved chat history for category: {category}, ID: {chat_id}")
        return chat_id
    except sqlite3.Error as e:
        import traceback
        logger.error(f"Failed to save chat history: {str(e)}\n{traceback.format_exc()}")
        return None
    finally:
        conn.close()

def update_session_end(session_id):
    """Update the session_end timestamp for a given session_id."""
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Check if session_end column exists
        cursor.execute("PRAGMA table_info(chat_history)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'session_end' not in column_names or 'session_id' not in column_names:
            logger.warning("Cannot update session end time: required columns missing")
            return False

        # Update the session_end timestamp for all entries with this session_id
        cursor.execute('''
            UPDATE chat_history
            SET session_end = CURRENT_TIMESTAMP
            WHERE session_id = ? AND session_end IS NULL
        ''', (session_id,))

        conn.commit()
        affected_rows = cursor.rowcount
        logger.info(f"Updated session end time for session {session_id}, {affected_rows} rows affected")
        return affected_rows > 0
    except sqlite3.Error as e:
        logger.error(f"Failed to update session end time: {str(e)}")
        return False
    finally:
        conn.close()

def get_chat_history(session_id=None):
    """
    Get chat history, optionally filtered by session_id.

    Args:
        session_id (str, optional): If provided, only returns history for this session.

    Returns:
        list: List of chat history entries.
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Check if columns exist
        cursor.execute("PRAGMA table_info(chat_history)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        has_metadata = 'metadata' in column_names
        has_url_images = 'url_images' in column_names
        has_pdf_images = 'pdf_images' in column_names
        has_document_thumbnails = 'document_thumbnails' in column_names
        has_client_name = 'client_name' in column_names
        has_session_id = 'session_id' in column_names
        has_session_start = 'session_start' in column_names
        has_session_end = 'session_end' in column_names
        has_device_fingerprint = 'device_fingerprint' in column_names
        has_anti_hallucination_mode = 'anti_hallucination_mode' in column_names
        has_model_name = 'model_name' in column_names
        has_embedding_model = 'embedding_model' in column_names
        has_vision_model = 'vision_model' in column_names

        # Build the SELECT query based on available columns
        select_columns = ['id', 'category', 'question', 'answer', 'sources', 'timestamp', 'images', 'pdf_links']

        if has_metadata:
            select_columns.append('metadata')

        if has_url_images:
            select_columns.append('url_images')

        if has_pdf_images:
            select_columns.append('pdf_images')

        if has_document_thumbnails:
            select_columns.append('document_thumbnails')

        if has_client_name:
            select_columns.append('client_name')

        if has_session_id:
            select_columns.append('session_id')

        if has_session_start:
            select_columns.append('session_start')

        if has_session_end:
            select_columns.append('session_end')

        if has_device_fingerprint:
            select_columns.append('device_fingerprint')

        if has_anti_hallucination_mode:
            select_columns.append('anti_hallucination_mode')

        if has_model_name:
            select_columns.append('model_name')

        if has_embedding_model:
            select_columns.append('embedding_model')

        if has_vision_model:
            select_columns.append('vision_model')

        # Build the query, filtering by session_id if provided
        query = f"SELECT {', '.join(select_columns)} FROM chat_history"

        # Add WHERE clause if filtering by session_id
        if session_id and has_session_id:
            query += f" WHERE session_id = ?"
            query += " ORDER BY timestamp ASC"  # ASC for session view to show conversation in order
            cursor.execute(query, (session_id,))
        else:
            query += " ORDER BY timestamp DESC"  # DESC for all history to show newest first
            cursor.execute(query)
        rows = cursor.fetchall()

        history = []
        for row in rows:
            entry = {
                "id": row[0],
                "category": row[1],
                "question": row[2],
                "answer": row[3],
                "sources": json.loads(row[4]) if row[4] else [],
                "timestamp": row[5],
                "images": json.loads(row[6]) if row[6] else [],
                "pdf_links": json.loads(row[7]) if row[7] else [],
            }

            # Add metadata if available
            if has_metadata:
                metadata_index = select_columns.index('metadata')
                entry["metadata"] = json.loads(row[metadata_index]) if row[metadata_index] else {}
            else:
                entry["metadata"] = {}  # Empty metadata for backward compatibility

            # Add url_images if available
            if has_url_images:
                url_images_index = select_columns.index('url_images')
                entry["url_images"] = json.loads(row[url_images_index]) if row[url_images_index] else []
            else:
                entry["url_images"] = []  # Empty url_images for backward compatibility

            # Add pdf_images if available
            if has_pdf_images:
                pdf_images_index = select_columns.index('pdf_images')
                entry["pdf_images"] = json.loads(row[pdf_images_index]) if row[pdf_images_index] else []
            else:
                entry["pdf_images"] = []  # Empty pdf_images for backward compatibility

            # Add document_thumbnails if available
            if has_document_thumbnails:
                document_thumbnails_index = select_columns.index('document_thumbnails')
                entry["document_thumbnails"] = json.loads(row[document_thumbnails_index]) if row[document_thumbnails_index] else []
            else:
                entry["document_thumbnails"] = []  # Empty document_thumbnails for backward compatibility

            # Add client_name if available
            if has_client_name:
                client_name_index = select_columns.index('client_name')
                entry["client_name"] = row[client_name_index] if row[client_name_index] else None
            else:
                entry["client_name"] = None  # No client name for backward compatibility

            # Add session_id if available
            if has_session_id:
                session_id_index = select_columns.index('session_id')
                entry["session_id"] = row[session_id_index] if row[session_id_index] else None
            else:
                entry["session_id"] = None  # No session ID for backward compatibility

            # Add session_start if available
            if has_session_start:
                session_start_index = select_columns.index('session_start')
                entry["session_start"] = row[session_start_index] if row[session_start_index] else None
            else:
                entry["session_start"] = None  # No session start for backward compatibility

            # Add session_end if available
            if has_session_end:
                session_end_index = select_columns.index('session_end')
                entry["session_end"] = row[session_end_index] if row[session_end_index] else None
            else:
                entry["session_end"] = None  # No session end for backward compatibility

            # Add device_fingerprint if available
            if has_device_fingerprint:
                device_fingerprint_index = select_columns.index('device_fingerprint')
                entry["device_fingerprint"] = row[device_fingerprint_index] if row[device_fingerprint_index] else None
            else:
                entry["device_fingerprint"] = None  # No device fingerprint for backward compatibility

            # Add anti_hallucination_mode if available
            if has_anti_hallucination_mode:
                anti_hallucination_mode_index = select_columns.index('anti_hallucination_mode')
                entry["anti_hallucination_mode"] = row[anti_hallucination_mode_index] if row[anti_hallucination_mode_index] else None
            else:
                entry["anti_hallucination_mode"] = None  # No anti-hallucination mode for backward compatibility

            # Add model_name if available
            if has_model_name:
                model_name_index = select_columns.index('model_name')
                entry["model_name"] = row[model_name_index] if row[model_name_index] else None
            else:
                entry["model_name"] = None  # No model name for backward compatibility

            # Add embedding_model if available
            if has_embedding_model:
                embedding_model_index = select_columns.index('embedding_model')
                entry["embedding_model"] = row[embedding_model_index] if row[embedding_model_index] else None
            else:
                entry["embedding_model"] = None  # No embedding model for backward compatibility

            # Add vision_model if available
            if has_vision_model:
                vision_model_index = select_columns.index('vision_model')
                entry["vision_model"] = row[vision_model_index] if row[vision_model_index] else None
            else:
                entry["vision_model"] = None  # No vision model for backward compatibility

            history.append(entry)

        return history
    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve chat history: {str(e)}")
        return []
    finally:
        conn.close()

def get_sessions():
    """
    Get a list of all unique sessions with their details.

    Returns:
        list: List of session objects with id, client_name, start_time, end_time, and message_count.
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Check if session columns exist
        cursor.execute("PRAGMA table_info(chat_history)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'session_id' not in column_names:
            logger.warning("Cannot get sessions: session_id column missing")
            return []

        # Check if device_fingerprint column exists
        has_device_fingerprint = 'device_fingerprint' in column_names

        # Query to get session information
        if has_device_fingerprint:
            query = """
                SELECT
                    session_id,
                    client_name,
                    MIN(timestamp) as start_time,
                    MAX(session_end) as end_time,
                    COUNT(*) as message_count,
                    category,
                    device_fingerprint
                FROM chat_history
                WHERE session_id IS NOT NULL
                GROUP BY session_id
                ORDER BY start_time DESC
            """
        else:
            query = """
                SELECT
                    session_id,
                    client_name,
                    MIN(timestamp) as start_time,
                    MAX(session_end) as end_time,
                    COUNT(*) as message_count,
                    category
                FROM chat_history
                WHERE session_id IS NOT NULL
                GROUP BY session_id
                ORDER BY start_time DESC
            """

        cursor.execute(query)
        rows = cursor.fetchall()

        sessions = []
        for row in rows:
            if has_device_fingerprint:
                session = {
                    "session_id": row[0],
                    "client_name": row[1] if row[1] else "Anonymous",
                    "start_time": row[2],
                    "end_time": row[3],
                    "message_count": row[4],
                    "category": row[5],
                    "device_fingerprint": row[6] if row[6] else "Unknown"
                }
            else:
                session = {
                    "session_id": row[0],
                    "client_name": row[1] if row[1] else "Anonymous",
                    "start_time": row[2],
                    "end_time": row[3],
                    "message_count": row[4],
                    "category": row[5],
                    "device_fingerprint": "Unknown"
                }
            sessions.append(session)

        return sessions
    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve sessions: {str(e)}")
        return []
    finally:
        conn.close()

def save_analytics(chat_id, session_id, category, client_name, question_length, answer_length,
                  processing_time, source_count, image_count, token_count=None,
                  model_name=None, embedding_model=None, vision_model=None, vision_enabled=False,
                  images_filtered=0, total_images_extracted=0, filter_sensitivity=None,
                  hallucination_detected=False, anti_hallucination_mode=None, device_fingerprint=None, ip_address=None,
                  city=None, region=None, country=None, latitude=None, longitude=None):
    """
    Save analytics data for a chat interaction.

    Args:
        chat_id (int): ID of the chat history entry
        session_id (str): Session ID
        category (str): Category of the chat
        client_name (str): Name of the client
        question_length (int): Length of the question in characters
        answer_length (int): Length of the answer in characters
        processing_time (float): Time taken to process the query in seconds
        source_count (int): Number of sources used
        image_count (int): Number of images included
        token_count (int, optional): Number of tokens used
        model_name (str, optional): Name of the LLM model used
        embedding_model (str, optional): Name of the embedding model used
        vision_model (str, optional): Name of the vision model used
        vision_enabled (bool, optional): Whether vision features are enabled
        images_filtered (int, optional): Number of images filtered out
        total_images_extracted (int, optional): Total number of images extracted before filtering
        filter_sensitivity (str, optional): Sensitivity level used for filtering (low, medium, high)
        hallucination_detected (bool, optional): Whether hallucination was detected
        device_fingerprint (str, optional): Device fingerprint for user identification
        ip_address (str, optional): User's IP address
        city (str, optional): User's city
        region (str, optional): User's region/state
        country (str, optional): User's country
        latitude (float, optional): Latitude coordinate for mapping
        longitude (float, optional): Longitude coordinate for mapping

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Check if anti_hallucination_mode column exists in analytics table
        cursor.execute("PRAGMA table_info(chat_analytics)")
        analytics_columns = cursor.fetchall()
        analytics_column_names = [column[1] for column in analytics_columns]
        has_anti_hallucination_mode_analytics = 'anti_hallucination_mode' in analytics_column_names

        # Add anti_hallucination_mode column to analytics table if it doesn't exist
        if not has_anti_hallucination_mode_analytics:
            try:
                cursor.execute('ALTER TABLE chat_analytics ADD COLUMN anti_hallucination_mode TEXT')
                logger.info("Added anti_hallucination_mode column to chat_analytics table.")
                has_anti_hallucination_mode_analytics = True
            except sqlite3.Error as e:
                logger.error(f"Failed to add anti_hallucination_mode column to analytics: {str(e)}")

        # Insert with anti_hallucination_mode if column exists
        if has_anti_hallucination_mode_analytics:
            cursor.execute('''
                INSERT INTO chat_analytics (
                    chat_id, session_id, category, client_name, question_length, answer_length,
                    processing_time, source_count, image_count, token_count, model_name,
                    embedding_model, vision_model, vision_enabled, images_filtered,
                    total_images_extracted, filter_sensitivity, hallucination_detected,
                    anti_hallucination_mode, device_fingerprint, ip_address, city, region, country, latitude, longitude
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                chat_id, session_id, category, client_name, question_length, answer_length,
                processing_time, source_count, image_count, token_count, model_name,
                embedding_model, vision_model, vision_enabled, images_filtered,
                total_images_extracted, filter_sensitivity, hallucination_detected,
                anti_hallucination_mode, device_fingerprint, ip_address, city, region, country, latitude, longitude
            ))
        else:
            # Fallback for older schema without anti_hallucination_mode
            cursor.execute('''
                INSERT INTO chat_analytics (
                    chat_id, session_id, category, client_name, question_length, answer_length,
                    processing_time, source_count, image_count, token_count, model_name,
                    embedding_model, vision_model, vision_enabled, images_filtered,
                    total_images_extracted, filter_sensitivity, hallucination_detected,
                    device_fingerprint, ip_address, city, region, country, latitude, longitude
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                chat_id, session_id, category, client_name, question_length, answer_length,
                processing_time, source_count, image_count, token_count, model_name,
                embedding_model, vision_model, vision_enabled, images_filtered,
                total_images_extracted, filter_sensitivity, hallucination_detected,
                device_fingerprint, ip_address, city, region, country, latitude, longitude
            ))

        conn.commit()
        logger.info(f"Saved analytics for chat ID {chat_id}")
        return True
    except sqlite3.Error as e:
        logger.error(f"Failed to save analytics: {str(e)}")
        return False
    finally:
        conn.close()

def get_model_performance_analysis(start_date=None, end_date=None):
    """
    Get model performance analysis for development research.

    Returns data comparing different models and anti-hallucination modes
    to help determine which configurations work best.

    Args:
        start_date (str, optional): Start date filter (YYYY-MM-DD)
        end_date (str, optional): End date filter (YYYY-MM-DD)

    Returns:
        dict: Analysis data including model comparisons and performance metrics
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Check if the new columns exist
        cursor.execute("PRAGMA table_info(chat_analytics)")
        analytics_columns = cursor.fetchall()
        analytics_column_names = [column[1] for column in analytics_columns]
        has_anti_hallucination_mode = 'anti_hallucination_mode' in analytics_column_names

        # Base query
        base_query = '''
            SELECT
                model_name,
                anti_hallucination_mode,
                COUNT(*) as query_count,
                AVG(processing_time) as avg_processing_time,
                AVG(answer_length) as avg_answer_length,
                AVG(source_count) as avg_source_count,
                SUM(CASE WHEN hallucination_detected = 1 THEN 1 ELSE 0 END) as hallucination_count,
                AVG(CASE WHEN hallucination_detected = 1 THEN 1.0 ELSE 0.0 END) * 100 as hallucination_rate,
                category,
                timestamp
            FROM chat_analytics
            WHERE model_name IS NOT NULL
        '''

        # Add date filters if provided
        params = []
        if start_date:
            base_query += " AND DATE(timestamp) >= ?"
            params.append(start_date)
        if end_date:
            base_query += " AND DATE(timestamp) <= ?"
            params.append(end_date)

        # Group by model and anti-hallucination mode
        if has_anti_hallucination_mode:
            group_query = base_query + " GROUP BY model_name, anti_hallucination_mode, category ORDER BY model_name, anti_hallucination_mode"
        else:
            group_query = base_query + " GROUP BY model_name, category ORDER BY model_name"

        cursor.execute(group_query, params)
        results = cursor.fetchall()

        # Process results into structured data
        analysis = {
            "model_comparisons": [],
            "anti_hallucination_effectiveness": [],
            "category_performance": {},
            "summary": {
                "total_queries": 0,
                "models_tested": set(),
                "anti_hallucination_modes_tested": set(),
                "categories_tested": set()
            }
        }

        for row in results:
            if has_anti_hallucination_mode:
                model_name, anti_hallucination_mode, query_count, avg_processing_time, avg_answer_length, avg_source_count, hallucination_count, hallucination_rate, category, timestamp = row
            else:
                model_name, _, query_count, avg_processing_time, avg_answer_length, avg_source_count, hallucination_count, hallucination_rate, category, timestamp = row
                anti_hallucination_mode = "Unknown"

            # Add to model comparisons
            comparison_entry = {
                "model_name": model_name,
                "anti_hallucination_mode": anti_hallucination_mode,
                "category": category,
                "query_count": query_count,
                "avg_processing_time": round(avg_processing_time, 3) if avg_processing_time else 0,
                "avg_answer_length": round(avg_answer_length, 1) if avg_answer_length else 0,
                "avg_source_count": round(avg_source_count, 1) if avg_source_count else 0,
                "hallucination_count": hallucination_count,
                "hallucination_rate": round(hallucination_rate, 2) if hallucination_rate else 0
            }
            analysis["model_comparisons"].append(comparison_entry)

            # Update summary
            analysis["summary"]["total_queries"] += query_count
            analysis["summary"]["models_tested"].add(model_name)
            if anti_hallucination_mode != "Unknown":
                analysis["summary"]["anti_hallucination_modes_tested"].add(anti_hallucination_mode)
            analysis["summary"]["categories_tested"].add(category)

            # Category performance tracking
            if category not in analysis["category_performance"]:
                analysis["category_performance"][category] = []
            analysis["category_performance"][category].append(comparison_entry)

        # Convert sets to lists for JSON serialization
        analysis["summary"]["models_tested"] = list(analysis["summary"]["models_tested"])
        analysis["summary"]["anti_hallucination_modes_tested"] = list(analysis["summary"]["anti_hallucination_modes_tested"])
        analysis["summary"]["categories_tested"] = list(analysis["summary"]["categories_tested"])

        # Get top performing configurations
        if analysis["model_comparisons"]:
            # Sort by lowest hallucination rate, then by fastest processing time
            sorted_by_performance = sorted(
                analysis["model_comparisons"],
                key=lambda x: (x["hallucination_rate"], x["avg_processing_time"])
            )
            analysis["top_configurations"] = sorted_by_performance[:5]

            # Get anti-hallucination effectiveness data
            if has_anti_hallucination_mode:
                hallucination_effectiveness = {}
                for entry in analysis["model_comparisons"]:
                    mode = entry["anti_hallucination_mode"]
                    if mode not in hallucination_effectiveness:
                        hallucination_effectiveness[mode] = {
                            "total_queries": 0,
                            "total_hallucinations": 0,
                            "avg_processing_time": 0,
                            "query_count": 0
                        }

                    hallucination_effectiveness[mode]["total_queries"] += entry["query_count"]
                    hallucination_effectiveness[mode]["total_hallucinations"] += entry["hallucination_count"]
                    hallucination_effectiveness[mode]["avg_processing_time"] += entry["avg_processing_time"] * entry["query_count"]
                    hallucination_effectiveness[mode]["query_count"] += entry["query_count"]

                # Calculate final averages
                for mode in hallucination_effectiveness:
                    data = hallucination_effectiveness[mode]
                    if data["query_count"] > 0:
                        data["hallucination_rate"] = round((data["total_hallucinations"] / data["total_queries"]) * 100, 2)
                        data["avg_processing_time"] = round(data["avg_processing_time"] / data["query_count"], 3)

                analysis["anti_hallucination_effectiveness"] = [
                    {"mode": mode, **data} for mode, data in hallucination_effectiveness.items()
                ]

        logger.info(f"Generated model performance analysis with {len(analysis['model_comparisons'])} entries")
        return analysis

    except sqlite3.Error as e:
        logger.error(f"Failed to get model performance analysis: {str(e)}")
        return {
            "model_comparisons": [],
            "anti_hallucination_effectiveness": [],
            "category_performance": {},
            "summary": {"error": str(e)}
        }
    finally:
        conn.close()

def get_analytics(session_id=None, client_name=None, device_fingerprint=None, start_date=None, end_date=None):
    """
    Get analytics data, optionally filtered by session_id, client_name, device_fingerprint, and date range.

    Args:
        session_id (str, optional): If provided, only returns analytics for this session
        client_name (str, optional): If provided, only returns analytics for this client
        device_fingerprint (str, optional): If provided, only returns analytics for this device
        start_date (str, optional): Start date for filtering (format: YYYY-MM-DD)
        end_date (str, optional): End date for filtering (format: YYYY-MM-DD)

    Returns:
        list: List of analytics entries
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Build the query with optional filters
        query = "SELECT * FROM chat_analytics"
        params = []

        # Add WHERE clause if any filters are provided
        filters = []

        if session_id:
            filters.append("session_id = ?")
            params.append(session_id)

        if client_name:
            filters.append("client_name = ?")
            params.append(client_name)

        if device_fingerprint:
            filters.append("device_fingerprint = ?")
            params.append(device_fingerprint)

        if start_date:
            filters.append("timestamp >= ?")
            params.append(f"{start_date} 00:00:00")

        if end_date:
            filters.append("timestamp <= ?")
            params.append(f"{end_date} 23:59:59")

        if filters:
            query += " WHERE " + " AND ".join(filters)

        query += " ORDER BY timestamp DESC"

        cursor.execute(query, params)
        rows = cursor.fetchall()

        # Get column names
        column_names = [description[0] for description in cursor.description]

        # Convert rows to dictionaries
        analytics = []
        for row in rows:
            entry = {column_names[i]: row[i] for i in range(len(column_names))}
            analytics.append(entry)

        return analytics
    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve analytics: {str(e)}")
        return []
    finally:
        conn.close()

def get_analytics_summary(client_name=None, device_fingerprint=None):
    """
    Get summary statistics from the analytics data.

    Args:
        client_name (str, optional): If provided, only returns analytics for this client
        device_fingerprint (str, optional): If provided, only returns analytics for this device

    Returns:
        dict: Dictionary containing summary statistics
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        summary = {}

        # Add filters if provided
        filter_clause = ""
        params = []

        if client_name:
            filter_clause = "WHERE client_name = ?"
            params.append(client_name)
            summary["client_name"] = client_name

        if device_fingerprint:
            if filter_clause:
                filter_clause += " AND device_fingerprint = ?"
            else:
                filter_clause = "WHERE device_fingerprint = ?"
            params.append(device_fingerprint)
            summary["device_fingerprint"] = device_fingerprint

        # Total number of queries
        cursor.execute(f"SELECT COUNT(*) FROM chat_analytics {filter_clause}", params)
        summary["total_queries"] = cursor.fetchone()[0]

        # Total number of unique sessions
        cursor.execute(f"SELECT COUNT(DISTINCT session_id) FROM chat_analytics {filter_clause}", params)
        summary["total_sessions"] = cursor.fetchone()[0]

        if not client_name and not device_fingerprint:
            # Total number of unique clients (only if not filtering by client or device)
            cursor.execute("SELECT COUNT(DISTINCT client_name) FROM chat_analytics WHERE client_name IS NOT NULL AND client_name != ''")
            summary["total_clients"] = cursor.fetchone()[0]

            # Total number of unique devices
            cursor.execute("SELECT COUNT(DISTINCT device_fingerprint) FROM chat_analytics WHERE device_fingerprint IS NOT NULL AND device_fingerprint != ''")
            summary["total_devices"] = cursor.fetchone()[0]

            # List of clients for dropdown (legacy)
            cursor.execute("""
                SELECT client_name, COUNT(*) as query_count
                FROM chat_analytics
                WHERE client_name IS NOT NULL AND client_name != ''
                GROUP BY client_name
                ORDER BY query_count DESC
            """)
            summary["clients"] = [{"name": row[0], "query_count": row[1]} for row in cursor.fetchall()]

            # List of devices for dropdown (with client names)
            cursor.execute("""
                SELECT
                    device_fingerprint,
                    COUNT(*) as query_count,
                    (SELECT client_name FROM chat_analytics
                     WHERE device_fingerprint = ca.device_fingerprint
                     AND client_name IS NOT NULL AND client_name != ''
                     ORDER BY timestamp DESC LIMIT 1) as client_name
                FROM chat_analytics ca
                WHERE device_fingerprint IS NOT NULL AND device_fingerprint != ''
                GROUP BY device_fingerprint
                ORDER BY query_count DESC
            """)
            summary["devices"] = [{"fingerprint": row[0], "query_count": row[1], "client_name": row[2]} for row in cursor.fetchall()]

            # Get common questions across all users/devices
            cursor.execute("""
                SELECT ch.question, COUNT(*) as count, ch.category
                FROM chat_history ch
                JOIN chat_analytics ca ON ch.id = ca.chat_id
                GROUP BY ch.question
                ORDER BY count DESC
                LIMIT 15
            """)
            summary["common_questions_global"] = [{"question": row[0], "count": row[1], "category": row[2]} for row in cursor.fetchall()]

        # Average processing time
        cursor.execute(f"SELECT AVG(processing_time) FROM chat_analytics {filter_clause} WHERE processing_time IS NOT NULL", params)
        avg_processing_time = cursor.fetchone()[0]
        summary["avg_processing_time"] = 0.0 if avg_processing_time is None else float(avg_processing_time)

        # Average question length
        cursor.execute(f"SELECT AVG(question_length) FROM chat_analytics {filter_clause} WHERE question_length IS NOT NULL", params)
        avg_question_length = cursor.fetchone()[0]
        summary["avg_question_length"] = 0 if avg_question_length is None else float(avg_question_length)

        # Average answer length
        cursor.execute(f"SELECT AVG(answer_length) FROM chat_analytics {filter_clause} WHERE answer_length IS NOT NULL", params)
        avg_answer_length = cursor.fetchone()[0]
        summary["avg_answer_length"] = 0 if avg_answer_length is None else float(avg_answer_length)

        # Average number of sources used
        cursor.execute(f"SELECT AVG(source_count) FROM chat_analytics {filter_clause} WHERE source_count IS NOT NULL", params)
        avg_source_count = cursor.fetchone()[0]
        summary["avg_source_count"] = 0 if avg_source_count is None else float(avg_source_count)

        # Percentage of queries with hallucinations
        cursor.execute(f"""
            SELECT
                (CAST(SUM(CASE WHEN hallucination_detected = 1 THEN 1 ELSE 0 END) AS FLOAT) /
                CASE WHEN COUNT(*) = 0 THEN 1 ELSE COUNT(*) END) * 100
            FROM chat_analytics
            {filter_clause}
        """, params)
        hallucination_percentage = cursor.fetchone()[0]
        summary["hallucination_percentage"] = 0.0 if hallucination_percentage is None else float(hallucination_percentage)

        # Most active categories
        cursor.execute(f"""
            SELECT category, COUNT(*) as count
            FROM chat_analytics
            {filter_clause}
            GROUP BY category
            ORDER BY count DESC
            LIMIT 5
        """, params)
        summary["top_categories"] = [{"category": row[0], "count": row[1]} for row in cursor.fetchall()]

        # Most used models
        if filter_clause:
            model_query = f"""
                SELECT model_name, COUNT(*) as count
                FROM chat_analytics
                WHERE model_name IS NOT NULL AND {filter_clause[6:]}
                GROUP BY model_name
                ORDER BY count DESC
                LIMIT 5
            """
        else:
            model_query = """
                SELECT model_name, COUNT(*) as count
                FROM chat_analytics
                WHERE model_name IS NOT NULL
                GROUP BY model_name
                ORDER BY count DESC
                LIMIT 5
            """
        cursor.execute(model_query, params)
        summary["top_models"] = [{"model": row[0], "count": row[1]} for row in cursor.fetchall()]

        # Get geolocation statistics
        if not device_fingerprint:
            # Top countries
            country_query = f"""
                SELECT country, COUNT(*) as count, COUNT(DISTINCT device_fingerprint) as unique_devices
                FROM chat_analytics
                WHERE country IS NOT NULL AND country != ''
                {filter_clause}
                GROUP BY country
                ORDER BY count DESC
                LIMIT 10
            """
            cursor.execute(country_query, params)
            summary["top_countries"] = [
                {"country": row[0], "count": row[1], "unique_devices": row[2]}
                for row in cursor.fetchall()
            ]

            # Top cities
            city_query = f"""
                SELECT city, region, country, COUNT(*) as count, COUNT(DISTINCT device_fingerprint) as unique_devices
                FROM chat_analytics
                WHERE city IS NOT NULL AND city != ''
                {filter_clause}
                GROUP BY city, country
                ORDER BY count DESC
                LIMIT 10
            """
            cursor.execute(city_query, params)
            summary["top_cities"] = [
                {
                    "city": row[0],
                    "region": row[1],
                    "country": row[2],
                    "count": row[3],
                    "unique_devices": row[4]
                }
                for row in cursor.fetchall()
            ]

        # Get common questions (for client-specific or device-specific analytics)
        if client_name or device_fingerprint:
            # Determine the filter to use
            if device_fingerprint:
                # Get chat history for this device to analyze questions
                cursor.execute("""
                    SELECT ch.question, COUNT(*) as count
                    FROM chat_history ch
                    JOIN chat_analytics ca ON ch.id = ca.chat_id
                    WHERE ca.device_fingerprint = ?
                    GROUP BY ch.question
                    ORDER BY count DESC
                    LIMIT 10
                """, [device_fingerprint])
                summary["common_questions"] = [{"question": row[0], "count": row[1]} for row in cursor.fetchall()]

                # Get session activity over time
                cursor.execute("""
                    SELECT DATE(timestamp) as date, COUNT(*) as count
                    FROM chat_analytics
                    WHERE device_fingerprint = ?
                    GROUP BY DATE(timestamp)
                    ORDER BY date
                """, [device_fingerprint])
                summary["activity_by_date"] = [{"date": row[0], "count": row[1]} for row in cursor.fetchall()]
            elif client_name:
                # Get chat history for this client to analyze questions (legacy)
                cursor.execute("""
                    SELECT ch.question, COUNT(*) as count
                    FROM chat_history ch
                    JOIN chat_analytics ca ON ch.id = ca.chat_id
                    WHERE ca.client_name = ?
                    GROUP BY ch.question
                    ORDER BY count DESC
                    LIMIT 10
                """, [client_name])
                summary["common_questions"] = [{"question": row[0], "count": row[1]} for row in cursor.fetchall()]

                # Get session activity over time
                cursor.execute("""
                    SELECT DATE(timestamp) as date, COUNT(*) as count
                    FROM chat_analytics
                    WHERE client_name = ?
                    GROUP BY DATE(timestamp)
                    ORDER BY date
                """, [client_name])
                summary["activity_by_date"] = [{"date": row[0], "count": row[1]} for row in cursor.fetchall()]

        return summary
    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve analytics summary: {str(e)}")
        return {}
    finally:
        conn.close()

def get_client_list():
    """
    Get a list of all clients who have used the system.

    Returns:
        list: List of client objects with name and query count
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT client_name, COUNT(*) as query_count
            FROM chat_analytics
            WHERE client_name IS NOT NULL AND client_name != ''
            GROUP BY client_name
            ORDER BY query_count DESC
        """)

        clients = [{"name": row[0], "query_count": row[1]} for row in cursor.fetchall()]
        return clients
    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve client list: {str(e)}")
        return []
    finally:
        conn.close()

# Location Management Functions

def save_extracted_location(location_data: dict) -> Optional[int]:
    """
    Save an extracted location to the database.

    Args:
        location_data: Dictionary containing location information

    Returns:
        Optional[int]: The ID of the inserted location, or None if failed
    """
    conn = None
    max_retries = 3
    retry_delay = 0.1

    # Validate location type before saving
    valid_location_types = ['place_name', 'address', 'coordinates', 'landmark', 'region', 'municipality', 'city', 'barangay']
    location_type = location_data.get('location_type')

    if not location_type or location_type not in valid_location_types:
        logger.warning(f"Invalid location type '{location_type}' for location '{location_data.get('location_text', 'Unknown')}'. Skipping save.")
        return None

    # Additional validation for location text
    location_text = location_data.get('location_text', '').strip()
    if not location_text or len(location_text) < 2:
        logger.warning(f"Invalid location text '{location_text}'. Skipping save.")
        return None

    for attempt in range(max_retries):
        try:
            # Use content database for location data
            from app.models.schema import DB_PATH
            conn = sqlite3.connect(DB_PATH, timeout=30.0)
            cursor = conn.cursor()

            # Determine administrative level based on location type
            administrative_level = None
            if location_data.get('location_type') in ['municipality', 'city', 'barangay']:
                administrative_level = location_data.get('location_type')

            cursor.execute("""
                INSERT INTO extracted_locations
                (location_text, location_type, latitude, longitude, confidence_score,
                 context_snippet, geocoded_address, country, region, city, municipality,
                 barangay, administrative_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                location_data.get('location_text'),
                location_data.get('location_type'),
                location_data.get('latitude'),
                location_data.get('longitude'),
                location_data.get('confidence_score', 0.0),
                location_data.get('context_snippet'),
                location_data.get('geocoded_address'),
                location_data.get('country'),
                location_data.get('region'),
                location_data.get('city'),
                location_data.get('municipality'),
                location_data.get('barangay'),
                administrative_level
            ))

            location_id = cursor.lastrowid
            conn.commit()

            logger.info(f"Saved extracted location: {location_data.get('location_text')} (ID: {location_id}, Type: {location_data.get('location_type')})")
            return location_id

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                logger.warning(f"Database locked while saving location on attempt {attempt + 1}, retrying in {retry_delay}s...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
                continue
            else:
                logger.error(f"Database error saving extracted location: {str(e)}")
                return None
        except sqlite3.Error as e:
            logger.error(f"Failed to save extracted location: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error saving extracted location: {str(e)}")
            return None
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass

    return None

def save_location_source(location_id: int, source_type: str, source_id: int,
                        page_number: int = None, extraction_method: str = 'ner') -> bool:
    """
    Save the source information for an extracted location.

    Args:
        location_id: ID of the extracted location
        source_type: Type of source ('pdf_document', 'chat_message', 'url_content')
        source_id: ID of the source document/message
        page_number: Page number for PDF sources
        extraction_method: Method used for extraction

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        from app.models.schema import DB_PATH
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO location_sources
            (location_id, source_type, source_id, page_number, extraction_method)
            VALUES (?, ?, ?, ?, ?)
        """, (location_id, source_type, source_id, page_number, extraction_method))

        conn.commit()
        conn.close()

        return True

    except sqlite3.Error as e:
        logger.error(f"Failed to save location source: {str(e)}")
        return False

def delete_pdf_locations(pdf_filename: str, category: str) -> bool:
    """
    Delete all location data associated with a specific PDF document.
    This function implements cascading deletion for location data.

    Args:
        pdf_filename: The filename of the PDF
        category: The category the PDF belongs to

    Returns:
        bool: True if successful, False otherwise
    """
    conn = None
    max_retries = 3
    retry_delay = 0.1

    for attempt in range(max_retries):
        try:
            from app.models.schema import DB_PATH
            from app.utils.content_db import get_pdf_document_id

            # Get the PDF document ID
            pdf_doc_id = get_pdf_document_id(pdf_filename, category)
            if not pdf_doc_id:
                logger.warning(f"PDF document not found in database: {pdf_filename} in category {category}")
                return True  # Consider it successful if the PDF doesn't exist in DB

            conn = sqlite3.connect(DB_PATH, timeout=30.0)
            cursor = conn.cursor()

            # Start transaction
            cursor.execute("BEGIN TRANSACTION")

            # Get all location IDs associated with this PDF
            cursor.execute("""
                SELECT DISTINCT ls.location_id
                FROM location_sources ls
                WHERE ls.source_type = 'pdf_document' AND ls.source_id = ?
            """, (pdf_doc_id,))

            location_ids = [row[0] for row in cursor.fetchall()]

            if not location_ids:
                logger.info(f"No locations found for PDF: {pdf_filename}")
                cursor.execute("COMMIT")
                return True

            # Delete location sources first (due to foreign key constraints)
            cursor.execute("""
                DELETE FROM location_sources
                WHERE source_type = 'pdf_document' AND source_id = ?
            """, (pdf_doc_id,))

            sources_deleted = cursor.rowcount

            # Delete extracted locations that are no longer referenced
            # Only delete locations that have no other sources
            for location_id in location_ids:
                cursor.execute("""
                    SELECT COUNT(*) FROM location_sources WHERE location_id = ?
                """, (location_id,))

                remaining_sources = cursor.fetchone()[0]
                if remaining_sources == 0:
                    # Delete from geocoding cache if this was the only reference
                    cursor.execute("""
                        SELECT location_text FROM extracted_locations WHERE id = ?
                    """, (location_id,))

                    location_text_result = cursor.fetchone()
                    if location_text_result:
                        location_text = location_text_result[0]
                        cursor.execute("""
                            DELETE FROM geocoding_cache WHERE location_query = ?
                        """, (location_text,))

                    # Delete the extracted location
                    cursor.execute("""
                        DELETE FROM extracted_locations WHERE id = ?
                    """, (location_id,))

            cursor.execute("COMMIT")

            logger.info(f"Deleted {sources_deleted} location sources and associated data for PDF: {pdf_filename}")
            return True

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                logger.warning(f"Database locked while deleting PDF locations on attempt {attempt + 1}, retrying in {retry_delay}s...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
                continue
            else:
                logger.error(f"Database error deleting PDF locations for {pdf_filename}: {str(e)}")
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                return False
        except sqlite3.Error as e:
            logger.error(f"Failed to delete PDF locations for {pdf_filename}: {str(e)}")
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting PDF locations for {pdf_filename}: {str(e)}")
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            return False
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass

    return False

def delete_location_by_id(location_id: int) -> bool:
    """
    Delete a specific location and all its associated data.

    Args:
        location_id: The ID of the location to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        from app.models.schema import DB_PATH
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get location text for cache cleanup
        cursor.execute("""
            SELECT location_text FROM extracted_locations WHERE id = ?
        """, (location_id,))

        result = cursor.fetchone()
        if not result:
            logger.warning(f"Location with ID {location_id} not found")
            conn.close()
            return True

        location_text = result[0]

        # Delete location sources (will cascade due to foreign key)
        cursor.execute("""
            DELETE FROM location_sources WHERE location_id = ?
        """, (location_id,))

        # Delete from geocoding cache
        cursor.execute("""
            DELETE FROM geocoding_cache WHERE location_query = ?
        """, (location_text,))

        # Delete the extracted location
        cursor.execute("""
            DELETE FROM extracted_locations WHERE id = ?
        """, (location_id,))

        conn.commit()
        conn.close()

        logger.info(f"Deleted location with ID {location_id}: {location_text}")
        return True

    except sqlite3.Error as e:
        logger.error(f"Failed to delete location with ID {location_id}: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def cleanup_orphaned_locations() -> dict:
    """
    Clean up orphaned location data (locations with no sources).

    Returns:
        dict: Statistics about cleaned data
    """
    try:
        from app.models.schema import DB_PATH
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Find orphaned locations (locations with no sources)
        cursor.execute("""
            SELECT el.id, el.location_text
            FROM extracted_locations el
            LEFT JOIN location_sources ls ON el.id = ls.location_id
            WHERE ls.location_id IS NULL
        """)

        orphaned_locations = cursor.fetchall()

        locations_deleted = 0
        cache_entries_deleted = 0

        for location_id, location_text in orphaned_locations:
            # Delete from geocoding cache
            cursor.execute("""
                DELETE FROM geocoding_cache WHERE location_query = ?
            """, (location_text,))

            if cursor.rowcount > 0:
                cache_entries_deleted += 1

            # Delete the orphaned location
            cursor.execute("""
                DELETE FROM extracted_locations WHERE id = ?
            """, (location_id,))

            if cursor.rowcount > 0:
                locations_deleted += 1

        conn.commit()
        conn.close()

        logger.info(f"Cleaned up {locations_deleted} orphaned locations and {cache_entries_deleted} cache entries")

        return {
            'locations_deleted': locations_deleted,
            'cache_entries_deleted': cache_entries_deleted
        }

    except sqlite3.Error as e:
        logger.error(f"Failed to cleanup orphaned locations: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return {
            'locations_deleted': 0,
            'cache_entries_deleted': 0,
            'error': str(e)
        }

def get_all_extracted_locations(include_sources: bool = True) -> list:
    """
    Get all extracted locations from the database.

    Args:
        include_sources: Whether to include source information

    Returns:
        list: List of location dictionaries
    """
    try:
        from app.models.schema import DB_PATH
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        if include_sources:
            cursor.execute("""
                SELECT el.id, el.location_text, el.location_type, el.latitude, el.longitude,
                       el.confidence_score, el.context_snippet, el.geocoded_address,
                       el.country, el.region, el.city, el.created_at,
                       ls.source_type, ls.source_id, ls.page_number, ls.extraction_method,
                       pd.filename, pd.category
                FROM extracted_locations el
                LEFT JOIN location_sources ls ON el.id = ls.location_id
                LEFT JOIN pdf_documents pd ON ls.source_type = 'pdf_document' AND ls.source_id = pd.id
                WHERE el.latitude IS NOT NULL AND el.longitude IS NOT NULL
                  AND LOWER(el.country) = 'philippines'
                ORDER BY el.created_at DESC
            """)
        else:
            cursor.execute("""
                SELECT id, location_text, location_type, latitude, longitude,
                       confidence_score, context_snippet, geocoded_address,
                       country, region, city, created_at
                FROM extracted_locations
                WHERE latitude IS NOT NULL AND longitude IS NOT NULL
                  AND LOWER(country) = 'philippines'
                ORDER BY created_at DESC
            """)

        results = cursor.fetchall()
        conn.close()

        locations = []
        for row in results:
            location = {
                'id': row[0],
                'location_text': row[1],
                'location_type': row[2],
                'latitude': row[3],
                'longitude': row[4],
                'confidence_score': row[5],
                'context_snippet': row[6],
                'geocoded_address': row[7],
                'country': row[8],
                'region': row[9],
                'city': row[10],
                'created_at': row[11]
            }

            if include_sources and len(row) > 12:
                location.update({
                    'source_type': row[12],
                    'source_id': row[13],
                    'page_number': row[14],
                    'extraction_method': row[15],
                    'source_filename': row[16],
                    'source_category': row[17]
                })

            locations.append(location)

        return locations

    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve extracted locations: {str(e)}")
        return []

def get_locations_by_category(category: str) -> list:
    """
    Get extracted locations filtered by document category.

    Args:
        category: Document category to filter by

    Returns:
        list: List of location dictionaries
    """
    try:
        from app.models.schema import DB_PATH
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT el.id, el.location_text, el.location_type, el.latitude, el.longitude,
                   el.confidence_score, el.context_snippet, el.geocoded_address,
                   el.country, el.region, el.city, el.created_at,
                   ls.source_type, ls.source_id, ls.page_number, ls.extraction_method,
                   pd.filename, pd.category
            FROM extracted_locations el
            JOIN location_sources ls ON el.id = ls.location_id
            JOIN pdf_documents pd ON ls.source_type = 'pdf_document' AND ls.source_id = pd.id
            WHERE pd.category = ? AND el.latitude IS NOT NULL AND el.longitude IS NOT NULL
            ORDER BY el.created_at DESC
        """, (category,))

        results = cursor.fetchall()
        conn.close()

        locations = []
        for row in results:
            location = {
                'id': row[0],
                'location_text': row[1],
                'location_type': row[2],
                'latitude': row[3],
                'longitude': row[4],
                'confidence_score': row[5],
                'context_snippet': row[6],
                'geocoded_address': row[7],
                'country': row[8],
                'region': row[9],
                'city': row[10],
                'created_at': row[11],
                'source_type': row[12],
                'source_id': row[13],
                'page_number': row[14],
                'extraction_method': row[15],
                'source_filename': row[16],
                'source_category': row[17]
            }
            locations.append(location)

        return locations

    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve locations by category: {str(e)}")
        return []

def get_location_statistics() -> dict:
    """
    Get statistics about extracted locations.

    Returns:
        dict: Statistics about locations
    """
    try:
        from app.models.schema import DB_PATH
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Total locations
        cursor.execute("SELECT COUNT(*) FROM extracted_locations")
        total_locations = cursor.fetchone()[0]

        # Geocoded locations
        cursor.execute("SELECT COUNT(*) FROM extracted_locations WHERE latitude IS NOT NULL AND longitude IS NOT NULL")
        geocoded_locations = cursor.fetchone()[0]

        # Locations by type
        cursor.execute("""
            SELECT location_type, COUNT(*)
            FROM extracted_locations
            GROUP BY location_type
            ORDER BY COUNT(*) DESC
        """)
        locations_by_type = [{'type': row[0], 'count': row[1]} for row in cursor.fetchall()]

        # Locations by source type
        cursor.execute("""
            SELECT ls.source_type, COUNT(*)
            FROM location_sources ls
            JOIN extracted_locations el ON ls.location_id = el.id
            GROUP BY ls.source_type
            ORDER BY COUNT(*) DESC
        """)
        locations_by_source = [{'source': row[0], 'count': row[1]} for row in cursor.fetchall()]

        # Top countries
        cursor.execute("""
            SELECT country, COUNT(*)
            FROM extracted_locations
            WHERE country IS NOT NULL AND country != ''
            GROUP BY country
            ORDER BY COUNT(*) DESC
            LIMIT 10
        """)
        top_countries = [{'country': row[0], 'count': row[1]} for row in cursor.fetchall()]

        conn.close()

        return {
            'total_locations': total_locations,
            'geocoded_locations': geocoded_locations,
            'geocoding_rate': (geocoded_locations / total_locations * 100) if total_locations > 0 else 0,
            'locations_by_type': locations_by_type,
            'locations_by_source': locations_by_source,
            'top_countries': top_countries
        }

    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve location statistics: {str(e)}")
        return {
            'total_locations': 0,
            'geocoded_locations': 0,
            'geocoding_rate': 0,
            'locations_by_type': [],
            'locations_by_source': [],
            'top_countries': []
        }